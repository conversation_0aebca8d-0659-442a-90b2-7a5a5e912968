import 'package:flutter/material.dart';

/// Responsive layout widget that adapts to different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          // Desktop layout
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= 600) {
          // Tablet layout
          return tablet ?? mobile;
        } else {
          // Mobile layout
          return mobile;
        }
      },
    );
  }
}

/// Responsive breakpoints
class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1200;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < tablet;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }
}

/// Responsive padding widget
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
  });

  @override
  Widget build(BuildContext context) {
    EdgeInsets padding;
    
    if (ResponsiveBreakpoints.isDesktop(context)) {
      padding = desktopPadding ?? tabletPadding ?? mobilePadding ?? const EdgeInsets.all(24);
    } else if (ResponsiveBreakpoints.isTablet(context)) {
      padding = tabletPadding ?? mobilePadding ?? const EdgeInsets.all(20);
    } else {
      padding = mobilePadding ?? const EdgeInsets.all(16);
    }

    return Padding(
      padding: padding,
      child: child,
    );
  }
}

/// Responsive spacing widget
class ResponsiveSpacing extends StatelessWidget {
  final double? mobile;
  final double? tablet;
  final double? desktop;
  final Axis direction;

  const ResponsiveSpacing({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.direction = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    double spacing;
    
    if (ResponsiveBreakpoints.isDesktop(context)) {
      spacing = desktop ?? tablet ?? mobile ?? 24;
    } else if (ResponsiveBreakpoints.isTablet(context)) {
      spacing = tablet ?? mobile ?? 20;
    } else {
      spacing = mobile ?? 16;
    }

    return direction == Axis.vertical
        ? SizedBox(height: spacing)
        : SizedBox(width: spacing);
  }
}

/// Responsive grid widget
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16,
    this.runSpacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    int columns;
    
    if (ResponsiveBreakpoints.isDesktop(context)) {
      columns = desktopColumns;
    } else if (ResponsiveBreakpoints.isTablet(context)) {
      columns = tabletColumns;
    } else {
      columns = mobileColumns;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Responsive flex widget
class ResponsiveFlex extends StatelessWidget {
  final List<Widget> children;
  final Axis mobileDirection;
  final Axis tabletDirection;
  final Axis desktopDirection;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  const ResponsiveFlex({
    super.key,
    required this.children,
    this.mobileDirection = Axis.vertical,
    this.tabletDirection = Axis.horizontal,
    this.desktopDirection = Axis.horizontal,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    Axis direction;
    
    if (ResponsiveBreakpoints.isDesktop(context)) {
      direction = desktopDirection;
    } else if (ResponsiveBreakpoints.isTablet(context)) {
      direction = tabletDirection;
    } else {
      direction = mobileDirection;
    }

    return Flex(
      direction: direction,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }
}

/// Responsive container with max width
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: maxWidth ?? 1200,
        ),
        padding: padding,
        margin: margin,
        child: child,
      ),
    );
  }
}

/// Responsive text size
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double? mobileSize;
  final double? tabletSize;
  final double? desktopSize;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.mobileSize,
    this.tabletSize,
    this.desktopSize,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    double fontSize;
    
    if (ResponsiveBreakpoints.isDesktop(context)) {
      fontSize = desktopSize ?? tabletSize ?? mobileSize ?? 16;
    } else if (ResponsiveBreakpoints.isTablet(context)) {
      fontSize = tabletSize ?? mobileSize ?? 16;
    } else {
      fontSize = mobileSize ?? 16;
    }

    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(fontSize: fontSize),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
