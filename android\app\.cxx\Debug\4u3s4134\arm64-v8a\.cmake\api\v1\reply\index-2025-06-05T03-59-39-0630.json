{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-e7b8a7c9dd293900ab5a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c6d0b9ffc214c35b454d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-a7152493f55b4fa58bf4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c6d0b9ffc214c35b454d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-a7152493f55b4fa58bf4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e7b8a7c9dd293900ab5a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}