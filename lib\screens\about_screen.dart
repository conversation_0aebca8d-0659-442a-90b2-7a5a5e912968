import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Screen displaying information about the developer
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('درباره توسعه‌دهنده'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildDeveloperProfileCard(context),
            const SizedBox(height: 16),
            _buildContactCard(context),
            const SizedBox(height: 16),
            _buildExpertiseCard(context),
            const SizedBox(height: 16),
            _buildVisionCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDeveloperProfileCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Developer avatar
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(AppConstants.primaryColor),
                    const Color(AppConstants.accentColor),
                  ],
                ),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.person,
                size: 50,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),

            // Developer name
            const Text(
              'سعد بصیر',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Title
            Text(
              'توسعه‌دهنده نرم‌افزار',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: const Color(AppConstants.primaryColor),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Brief description
            Text(
              'متخصص در طراحی و پیاده‌سازی راه‌حل‌های نوآورانه دیجیتال',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'اطلاعات تماس',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            _buildContactItem(
              Icons.phone,
              'شماره تماس',
              '۰۷۹۲۴۳۶۸۰۰',
            ),
            const SizedBox(height: 16),

            _buildContactItem(
              Icons.email,
              'ایمیل',
              '<EMAIL>',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpertiseCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.engineering,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'حوزه‌های تخصصی',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildExpertiseItem(
              Icons.auto_awesome,
              'اتوماسیون',
              'طراحی و پیاده‌سازی سیستم‌های خودکار',
            ),
            const SizedBox(height: 12),

            _buildExpertiseItem(
              Icons.transform,
              'تحول دیجیتال',
              'ارتقاء فرآیندها با استفاده از فناوری‌های نوین',
            ),
            const SizedBox(height: 12),

            _buildExpertiseItem(
              Icons.architecture,
              'معماری تمیز',
              'طراحی نرم‌افزار با اصول مهندسی نرم‌افزار',
            ),
            const SizedBox(height: 12),

            _buildExpertiseItem(
              Icons.science,
              'نگارش علمی',
              'رویکرد تحقیق‌محور در توسعه راه‌حل‌ها',
            ),
            const SizedBox(height: 12),

            _buildExpertiseItem(
              Icons.public,
              'راه‌حل‌های محلی',
              'ابزارهای کاربردی برای چالش‌های افغانستان',
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildVisionCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.visibility,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'رویکرد و هدف',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'سعد بصیر توسعه‌دهنده نرم‌افزار با تمرکز بر طراحی و پیاده‌سازی راه‌حل‌های نوآورانه دیجیتال است. وی در حوزه اتوماسیون، تحول دیجیتال، و توسعه ابزارهای کاربردی برای حل چالش‌های واقعی افغانستان تخصص دارد.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),

            Text(
              'رویکرد ایشان بر مبنای معماری تمیز، نگارش علمی دقیق و استفاده از فناوری‌های روز دنیا است. هدف وی ارائه راه‌کارهایی تحقیق‌محور و کاربردی برای ارتقاء خدمات دیجیتال در کشور می‌باشد.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(AppConstants.primaryColor).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: const Color(AppConstants.primaryColor),
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExpertiseItem(IconData icon, String title, String description) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: const Color(AppConstants.accentColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 18),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
