import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Screen displaying information about the app and developer
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('درباره برنامه و سازنده'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildAppInfoCard(context),
            const SizedBox(height: 16),
            _buildDeveloperCard(context),
            const SizedBox(height: 16),
            _buildFeaturesCard(context),
            const SizedBox(height: 16),
            _buildTechnicalInfoCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfoCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // App icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(AppConstants.primaryColor),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(Icons.landscape, size: 40, color: Colors.white),
            ),
            const SizedBox(height: 16),
            
            // App name
            Text(
              AppConstants.appName,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(AppConstants.primaryColor),
              ),
            ),
            const SizedBox(height: 8),
            
            // App description
            Text(
              AppConstants.appDescription,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            // Version info
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(AppConstants.accentColor),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'نسخه ۱.۰.۰',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeveloperCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'درباره سازنده',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                // Developer profile image
                ClipRRect(
                  borderRadius: BorderRadius.circular(40),
                  child: Image.asset(
                    'assets/images/dev_profile.jpg',
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: const Color(AppConstants.secondaryColor),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'سعد بصیر',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'توسعه‌دهنده نرم‌افزار و راه‌حل‌های دیجیتال',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'متخصص در طراحی و پیاده‌سازی راه‌حل‌های دیجیتال نوآورانه با تمرکز بر اتوماسیون و تحول دیجیتال. مسلط به اصول توسعه مدرن، معماری تمیز و نگارش علمی، با علاقه به ایجاد ابزارهای کاربردی برای حل چالش‌های واقعی افغانستان از طریق فناوری‌های پیشرفته و رویکرد تحقیق‌محور.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'ویژگی‌های برنامه',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildFeatureItem(
              Icons.timeline,
              'الگوریتم Shoelace',
              'محاسبه دقیق مساحت با استفاده از مختصات نقاط',
            ),
            const SizedBox(height: 12),
            
            _buildFeatureItem(
              Icons.straighten,
              'روش سنتی',
              'محاسبه مساحت با استفاده از طول اضلاع و زوایا',
            ),
            const SizedBox(height: 12),
            
            _buildFeatureItem(
              Icons.swap_horiz,
              'تبدیل واحدها',
              'پشتیبانی از متر مربع، جریب، هکتار و سایر واحدها',
            ),
            const SizedBox(height: 12),
            
            _buildFeatureItem(
              Icons.history,
              'تاریخچه محاسبات',
              'ذخیره و مشاهده محاسبات قبلی',
            ),
            const SizedBox(height: 12),
            
            _buildFeatureItem(
              Icons.language,
              'پشتیبانی از زبان فارسی',
              'رابط کاربری کاملاً فارسی و راست‌چین',
            ),
            const SizedBox(height: 12),

            _buildFeatureItem(
              Icons.location_on,
              'طراحی برای افغانستان',
              'ابزاری کاربردی برای کشاورزان و متخصصان املاک',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: const Color(AppConstants.accentColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 18),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTechnicalInfoCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'اطلاعات فنی',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildTechnicalItem('پلتفرم', 'Flutter'),
            const SizedBox(height: 8),
            _buildTechnicalItem('زبان برنامه‌نویسی', 'Dart'),
            const SizedBox(height: 8),
            _buildTechnicalItem('مدیریت وضعیت', 'Riverpod'),
            const SizedBox(height: 8),
            _buildTechnicalItem('ذخیره‌سازی محلی', 'SharedPreferences'),
            const SizedBox(height: 8),
            _buildTechnicalItem('پشتیبانی از', 'Android, iOS'),
            const SizedBox(height: 16),
            
            Text(
              'این برنامه با استفاده از تکنولوژی‌های مدرن و الگوریتم‌های دقیق ریاضی توسعه یافته است تا بهترین تجربه کاربری را در محاسبه مساحت زمین‌های نامنظم فراهم کند. هدف این پروژه ارائه راه‌حلی کاربردی و دقیق برای کشاورزان، مهندسان و متخصصان املاک در افغانستان است تا بتوانند به راحتی و با دقت بالا مساحت زمین‌های خود را محاسبه کنند.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalItem(String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
