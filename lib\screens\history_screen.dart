import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculation_result.dart';
import '../services/calculation_history_service.dart';
import '../services/unit_converter.dart';
import '../utils/constants.dart';
import 'results_screen.dart';

/// Screen for displaying calculation history
class HistoryScreen extends ConsumerStatefulWidget {
  const HistoryScreen({super.key});

  @override
  ConsumerState<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends ConsumerState<HistoryScreen> {
  List<CalculationResult> _calculations = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadHistory();
  }

  Future<void> _loadHistory() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final history = await CalculationHistoryService.getHistory();
      setState(() {
        _calculations = history.results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطا در بارگذاری تاریخچه';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تاریخچه محاسبات'),
        centerTitle: true,
        actions: [
          if (_calculations.isNotEmpty)
            IconButton(
              onPressed: _showClearHistoryDialog,
              icon: const Icon(Icons.delete_sweep),
              tooltip: 'پاک کردن تاریخچه',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadHistory,
              icon: const Icon(Icons.refresh),
              label: const Text('تلاش مجدد'),
            ),
          ],
        ),
      );
    }

    if (_calculations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'هیچ محاسبه‌ای در تاریخچه وجود ندارد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'محاسبات شما پس از ذخیره در اینجا نمایش داده خواهند شد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadHistory,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _calculations.length,
        itemBuilder: (context, index) {
          final calculation = _calculations[index];
          return _buildCalculationCard(calculation, index);
        },
      ),
    );
  }

  Widget _buildCalculationCard(CalculationResult calculation, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _viewCalculationDetails(calculation),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    calculation.method == CalculationMethod.coordinate
                        ? Icons.timeline
                        : Icons.straighten,
                    color: const Color(AppConstants.primaryColor),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      calculation.name ?? 'محاسبه بدون نام',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _deleteCalculation(index),
                    icon: const Icon(Icons.delete_outline),
                    iconSize: 20,
                    color: Colors.red[600],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'مساحت',
                      '${calculation.getFormattedArea()} ${UnitConverter.getUnitDisplayName(calculation.unit)}',
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'روش',
                      calculation.method == CalculationMethod.coordinate
                          ? 'مختصاتی'
                          : 'سنتی',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildInfoItem(
                'تاریخ',
                _formatDateTime(calculation.calculatedAt),
              ),
              if (calculation.notes != null && calculation.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildNotesSection(calculation.notes!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection(String notes) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.note,
            size: 16,
            color: Colors.blue[700],
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'یادداشت:',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  notes,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[800],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewCalculationDetails(CalculationResult calculation) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ResultsScreen(result: calculation),
      ),
    );
  }

  Future<void> _deleteCalculation(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف محاسبه'),
        content: const Text('آیا مطمئن هستید که می‌خواهید این محاسبه را حذف کنید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('انصراف'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await CalculationHistoryService.removeCalculation(index);
      if (success) {
        _loadHistory();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('محاسبه حذف شد'),
              backgroundColor: Color(AppConstants.primaryColor),
            ),
          );
        }
      }
    }
  }

  Future<void> _showClearHistoryDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('پاک کردن تاریخچه'),
        content: const Text('آیا مطمئن هستید که می‌خواهید تمام تاریخچه را پاک کنید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('انصراف'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('پاک کردن'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await CalculationHistoryService.clearHistory();
      if (success) {
        _loadHistory();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تاریخچه پاک شد'),
              backgroundColor: Color(AppConstants.primaryColor),
            ),
          );
        }
      }
    }
  }
}
