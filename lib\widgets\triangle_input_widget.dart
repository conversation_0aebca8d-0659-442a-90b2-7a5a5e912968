import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/constants.dart';
import '../services/geometric_calculator.dart';

/// Widget for inputting triangle side lengths
class TriangleInputWidget extends StatefulWidget {
  final double sideA;
  final double sideB;
  final double sideC;
  final ValueChanged<double> onSideAChanged;
  final ValueChanged<double> onSideBChanged;
  final ValueChanged<double> onSideCChanged;

  const TriangleInputWidget({
    super.key,
    required this.sideA,
    required this.sideB,
    required this.sideC,
    required this.onSideAChanged,
    required this.onSideBChanged,
    required this.onSideCChanged,
  });

  @override
  State<TriangleInputWidget> createState() => _TriangleInputWidgetState();
}

class _TriangleInputWidgetState extends State<TriangleInputWidget> {
  late TextEditingController _sideAController;
  late TextEditingController _sideBController;
  late TextEditingController _sideCController;
  late FocusNode _sideAFocus;
  late FocusNode _sideBFocus;
  late FocusNode _sideCFocus;

  @override
  void initState() {
    super.initState();
    _sideAController = TextEditingController(
      text: widget.sideA == 0 ? '' : widget.sideA.toString(),
    );
    _sideBController = TextEditingController(
      text: widget.sideB == 0 ? '' : widget.sideB.toString(),
    );
    _sideCController = TextEditingController(
      text: widget.sideC == 0 ? '' : widget.sideC.toString(),
    );
    
    _sideAFocus = FocusNode();
    _sideBFocus = FocusNode();
    _sideCFocus = FocusNode();

    _sideAController.addListener(_onSideAChanged);
    _sideBController.addListener(_onSideBChanged);
    _sideCController.addListener(_onSideCChanged);
  }

  @override
  void dispose() {
    _sideAController.dispose();
    _sideBController.dispose();
    _sideCController.dispose();
    _sideAFocus.dispose();
    _sideBFocus.dispose();
    _sideCFocus.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(TriangleInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.sideA != widget.sideA) {
      _sideAController.text = widget.sideA == 0 ? '' : widget.sideA.toString();
    }
    if (oldWidget.sideB != widget.sideB) {
      _sideBController.text = widget.sideB == 0 ? '' : widget.sideB.toString();
    }
    if (oldWidget.sideC != widget.sideC) {
      _sideCController.text = widget.sideC == 0 ? '' : widget.sideC.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.primaryColor),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.change_history,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'فرمول هرون',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'محاسبه مساحت مثلث با سه ضلع',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Input fields
            _buildSideInput(
              label: AppConstants.uiText['side_a']!,
              controller: _sideAController,
              focusNode: _sideAFocus,
              nextFocusNode: _sideBFocus,
              icon: Icons.looks_one,
            ),
            
            const SizedBox(height: 16),
            
            _buildSideInput(
              label: AppConstants.uiText['side_b']!,
              controller: _sideBController,
              focusNode: _sideBFocus,
              nextFocusNode: _sideCFocus,
              icon: Icons.looks_two,
            ),
            
            const SizedBox(height: 16),
            
            _buildSideInput(
              label: AppConstants.uiText['side_c']!,
              controller: _sideCController,
              focusNode: _sideCFocus,
              icon: Icons.looks_3,
            ),
            
            const SizedBox(height: 20),
            
            // Triangle validation info
            _buildValidationInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSideInput({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocusNode,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          decoration: InputDecoration(
            hintText: 'طول ضلع (متر)',
            prefixIcon: Icon(icon),
            suffixText: 'متر',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          onFieldSubmitted: (_) {
            if (nextFocusNode != null) {
              nextFocusNode.requestFocus();
            }
          },
        ),
      ],
    );
  }

  Widget _buildValidationInfo() {
    final bool isValid = GeometricCalculator.validateTriangleInputs(
      widget.sideA,
      widget.sideB,
      widget.sideC,
    );

    if (widget.sideA == 0 || widget.sideB == 0 || widget.sideC == 0) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'لطفاً طول هر سه ضلع مثلث را وارد کنید',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      );
    }

    if (!isValid) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[700], size: 20),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'اضلاع وارد شده مثلث معتبری تشکیل نمی‌دهند',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      );
    }

    // Show triangle properties if valid
    try {
      final properties = GeometricCalculator.getTriangleProperties(
        sideA: widget.sideA,
        sideB: widget.sideB,
        sideC: widget.sideC,
      );

      final area = properties['area'] as double;
      final perimeter = properties['perimeter'] as double;

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[700], size: 20),
                const SizedBox(width: 8),
                const Text(
                  'مثلث معتبر',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'مساحت: ${area.toStringAsFixed(2)} متر مربع',
              style: const TextStyle(fontSize: 13),
            ),
            Text(
              'محیط: ${perimeter.toStringAsFixed(2)} متر',
              style: const TextStyle(fontSize: 13),
            ),
          ],
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  void _onSideAChanged() {
    final text = _sideAController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.sideA) {
      widget.onSideAChanged(value);
    }
  }

  void _onSideBChanged() {
    final text = _sideBController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.sideB) {
      widget.onSideBChanged(value);
    }
  }

  void _onSideCChanged() {
    final text = _sideCController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.sideC) {
      widget.onSideCChanged(value);
    }
  }
}
