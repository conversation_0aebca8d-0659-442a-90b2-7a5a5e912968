{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/bin/ctest.exe", "root": "C:/DEV/MobDev/android/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-291280f3aa53e5f8e778.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-6d83dce68406be4b7c4c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-de84e9a31d298fc30e23.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-6d83dce68406be4b7c4c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-de84e9a31d298fc30e23.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-291280f3aa53e5f8e778.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}