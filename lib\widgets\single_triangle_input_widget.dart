import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/triangle.dart';
import '../utils/constants.dart';
import '../services/geometric_calculator.dart';

/// Widget for inputting a single triangle within a multi-triangle shape
class SingleTriangleInputWidget extends StatefulWidget {
  final Triangle triangle;
  final int index;
  final ValueChanged<Triangle> onChanged;
  final VoidCallback? onRemove;

  const SingleTriangleInputWidget({
    super.key,
    required this.triangle,
    required this.index,
    required this.onChanged,
    this.onRemove,
  });

  @override
  State<SingleTriangleInputWidget> createState() => _SingleTriangleInputWidgetState();
}

class _SingleTriangleInputWidgetState extends State<SingleTriangleInputWidget> {
  late TextEditingController _sideAController;
  late TextEditingController _sideBController;
  late TextEditingController _sideCController;
  late FocusNode _sideAFocus;
  late FocusNode _sideBFocus;
  late FocusNode _sideCFocus;

  @override
  void initState() {
    super.initState();
    _sideAController = TextEditingController(
      text: widget.triangle.sideA == 0 ? '' : widget.triangle.sideA.toString(),
    );
    _sideBController = TextEditingController(
      text: widget.triangle.sideB == 0 ? '' : widget.triangle.sideB.toString(),
    );
    _sideCController = TextEditingController(
      text: widget.triangle.sideC == 0 ? '' : widget.triangle.sideC.toString(),
    );
    
    _sideAFocus = FocusNode();
    _sideBFocus = FocusNode();
    _sideCFocus = FocusNode();

    _sideAController.addListener(_onSideAChanged);
    _sideBController.addListener(_onSideBChanged);
    _sideCController.addListener(_onSideCChanged);
  }

  @override
  void dispose() {
    _sideAController.dispose();
    _sideBController.dispose();
    _sideCController.dispose();
    _sideAFocus.dispose();
    _sideBFocus.dispose();
    _sideCFocus.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(SingleTriangleInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.triangle.sideA != widget.triangle.sideA) {
      _sideAController.text = widget.triangle.sideA == 0 ? '' : widget.triangle.sideA.toString();
    }
    if (oldWidget.triangle.sideB != widget.triangle.sideB) {
      _sideBController.text = widget.triangle.sideB == 0 ? '' : widget.triangle.sideB.toString();
    }
    if (oldWidget.triangle.sideC != widget.triangle.sideC) {
      _sideCController.text = widget.triangle.sideC == 0 ? '' : widget.triangle.sideC.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: widget.triangle.isValid 
                        ? const Color(AppConstants.primaryColor)
                        : Colors.grey[400],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Center(
                    child: Text(
                      '${widget.index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'مثلث ${widget.index + 1}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.onRemove != null)
                  IconButton(
                    onPressed: widget.onRemove,
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red[600],
                    tooltip: 'حذف مثلث',
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Input fields in a row for compact layout
            Row(
              children: [
                Expanded(
                  child: _buildSideInput(
                    label: 'ضلع الف',
                    controller: _sideAController,
                    focusNode: _sideAFocus,
                    nextFocusNode: _sideBFocus,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSideInput(
                    label: 'ضلع ب',
                    controller: _sideBController,
                    focusNode: _sideBFocus,
                    nextFocusNode: _sideCFocus,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSideInput(
                    label: 'ضلع ج',
                    controller: _sideCController,
                    focusNode: _sideCFocus,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Validation and info
            _buildValidationInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSideInput({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocusNode,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          decoration: InputDecoration(
            hintText: '0.0',
            suffixText: 'م',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 14),
          onFieldSubmitted: (_) {
            if (nextFocusNode != null) {
              nextFocusNode.requestFocus();
            }
          },
        ),
      ],
    );
  }

  Widget _buildValidationInfo() {
    if (!widget.triangle.isComplete) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          children: [
            Icon(Icons.edit, color: Colors.grey[600], size: 16),
            const SizedBox(width: 6),
            const Expanded(
              child: Text(
                'لطفاً طول هر سه ضلع را وارد کنید',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      );
    }

    if (!widget.triangle.isValid) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[700], size: 16),
            const SizedBox(width: 6),
            const Expanded(
              child: Text(
                'اضلاع وارد شده مثلث معتبری تشکیل نمی‌دهند',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      );
    }

    // Show triangle area if valid
    try {
      final area = GeometricCalculator.calculateTriangleAreaHeron(
        widget.triangle.sideA,
        widget.triangle.sideB,
        widget.triangle.sideC,
      );

      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green[700], size: 16),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                'مساحت: ${area.toStringAsFixed(2)} متر مربع',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  void _onSideAChanged() {
    final text = _sideAController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.triangle.sideA) {
      widget.onChanged(widget.triangle.copyWith(sideA: value));
    }
  }

  void _onSideBChanged() {
    final text = _sideBController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.triangle.sideB) {
      widget.onChanged(widget.triangle.copyWith(sideB: value));
    }
  }

  void _onSideCChanged() {
    final text = _sideCController.text;
    final value = text.isEmpty ? 0.0 : (double.tryParse(text) ?? 0.0);
    if (value != widget.triangle.sideC) {
      widget.onChanged(widget.triangle.copyWith(sideC: value));
    }
  }
}
