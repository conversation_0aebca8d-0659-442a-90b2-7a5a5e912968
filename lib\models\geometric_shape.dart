import 'dart:math' as math;
/// Base class for geometric shapes
abstract class GeometricShape {
  final String name;
  final DateTime createdAt;

   GeometricShape({
    required this.name,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Calculate area of the shape
  double calculateArea();

  /// Calculate perimeter of the shape
  double calculatePerimeter();

  /// Get shape type identifier
  String get shapeType;

  /// Validate shape parameters
  bool isValid();

  /// Convert to JSON
  Map<String, dynamic> toJson();
}

/// Triangle shape with three sides
class Triangle extends GeometricShape {
  final double sideA;
  final double sideB;
  final double sideC;

  Triangle({
    required this.sideA,
    required this.sideB,
    required this.sideC,
    required super.name,
    super.createdAt,
  });

  /// Create triangle from JSON
  factory Triangle.fromJson(Map<String, dynamic> json) {
    return Triangle(
      sideA: (json['side_a'] as num).toDouble(),
      sideB: (json['side_b'] as num).toDouble(),
      sideC: (json['side_c'] as num).toDouble(),
      name: json['name'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  double calculateArea() {
    // Using Heron's formula
    final s = (sideA + sideB + sideC) / 2;
    return math.sqrt(s * (s - sideA) * (s - sideB) * (s - sideC));
  }

  @override
  double calculatePerimeter() {
    return sideA + sideB + sideC;
  }

  @override
  String get shapeType => 'triangle';

  @override
  bool isValid() {
    return sideA > 0 && sideB > 0 && sideC > 0 &&
           (sideA + sideB > sideC) &&
           (sideA + sideC > sideB) &&
           (sideB + sideC > sideA);
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'shape_type': shapeType,
      'side_a': sideA,
      'side_b': sideB,
      'side_c': sideC,
      'name': name,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Get triangle type
  String getTriangleType() {
    const double tolerance = 1e-10;
    
    // Sort sides
    final sides = [sideA, sideB, sideC]..sort();
    final double small = sides[0];
    final double medium = sides[1];
    final double large = sides[2];

    // Check for right triangle
    if ((small * small + medium * medium - large * large).abs() < tolerance) {
      return 'قائم‌الزاویه';
    }

    // Check for equilateral triangle
    if ((sideA - sideB).abs() < tolerance && (sideB - sideC).abs() < tolerance) {
      return 'متساوی‌الاضلاع';
    }

    // Check for isosceles triangle
    if ((sideA - sideB).abs() < tolerance || 
        (sideB - sideC).abs() < tolerance || 
        (sideA - sideC).abs() < tolerance) {
      return 'متساوی‌الساقین';
    }

    // Check for acute or obtuse triangle
    if (small * small + medium * medium > large * large) {
      return 'حادالزاویه';
    } else {
      return 'منفرج‌الزاویه';
    }
  }

  /// Calculate angles in degrees
  Map<String, double> getAngles() {
    final angleA = math.acos((sideB * sideB + sideC * sideC - sideA * sideA) / (2 * sideB * sideC));
    final angleB = math.acos((sideA * sideA + sideC * sideC - sideB * sideB) / (2 * sideA * sideC));
    final angleC = math.acos((sideA * sideA + sideB * sideB - sideC * sideC) / (2 * sideA * sideB));

    return {
      'angle_a': angleA * 180 / math.pi,
      'angle_b': angleB * 180 / math.pi,
      'angle_c': angleC * 180 / math.pi,
    };
  }

  /// Calculate inradius
  double getInradius() {
    return calculateArea() / (calculatePerimeter() / 2);
  }

  /// Calculate circumradius
  double getCircumradius() {
    return (sideA * sideB * sideC) / (4 * calculateArea());
  }

  Triangle copyWith({
    double? sideA,
    double? sideB,
    double? sideC,
    String? name,
    DateTime? createdAt,
  }) {
    return Triangle(
      sideA: sideA ?? this.sideA,
      sideB: sideB ?? this.sideB,
      sideC: sideC ?? this.sideC,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Triangle &&
           other.sideA == sideA &&
           other.sideB == sideB &&
           other.sideC == sideC &&
           other.name == name;
  }

  @override
  int get hashCode => Object.hash(sideA, sideB, sideC, name);

  @override
  String toString() => 'Triangle(a: $sideA, b: $sideB, c: $sideC)';
}

/// Rectangle shape with length and width
class Rectangle extends GeometricShape {
  final double length;
  final double width;

  Rectangle({
    required this.length,
    required this.width,
    required super.name,
    super.createdAt,
  });

  /// Create rectangle from JSON
  factory Rectangle.fromJson(Map<String, dynamic> json) {
    return Rectangle(
      length: (json['length'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      name: json['name'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  double calculateArea() {
    return length * width;
  }

  @override
  double calculatePerimeter() {
    return 2 * (length + width);
  }

  @override
  String get shapeType => 'rectangle';

  @override
  bool isValid() {
    return length > 0 && width > 0;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'shape_type': shapeType,
      'length': length,
      'width': width,
      'name': name,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Calculate diagonal
  double getDiagonal() {
    return math.sqrt(length * length + width * width);
  }

  /// Get aspect ratio
  double getAspectRatio() {
    return length / width;
  }

  /// Check if it's a square
  bool isSquare() {
    const double tolerance = 1e-10;
    return (length - width).abs() < tolerance;
  }

  Rectangle copyWith({
    double? length,
    double? width,
    String? name,
    DateTime? createdAt,
  }) {
    return Rectangle(
      length: length ?? this.length,
      width: width ?? this.width,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Rectangle &&
           other.length == length &&
           other.width == width &&
           other.name == name;
  }

  @override
  int get hashCode => Object.hash(length, width, name);

  @override
  String toString() => 'Rectangle(length: $length, width: $width)';
}

/// Regular polygon with n sides
class RegularPolygon extends GeometricShape {
  final int sides;
  final double sideLength;

  RegularPolygon({
    required this.sides,
    required this.sideLength,
    required super.name,
    super.createdAt,
  });

  /// Create regular polygon from JSON
  factory RegularPolygon.fromJson(Map<String, dynamic> json) {
    return RegularPolygon(
      sides: json['sides'],
      sideLength: (json['side_length'] as num).toDouble(),
      name: json['name'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  double calculateArea() {
    final double n = sides.toDouble();
    return (n * sideLength * sideLength) / (4 * math.tan(math.pi / n));
  }

  @override
  double calculatePerimeter() {
    return sides * sideLength;
  }

  @override
  String get shapeType => 'regular_polygon';

  @override
  bool isValid() {
    return sides >= 3 && sideLength > 0;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'shape_type': shapeType,
      'sides': sides,
      'side_length': sideLength,
      'name': name,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Get interior angle in degrees
  double getInteriorAngle() {
    return ((sides - 2) * 180.0) / sides;
  }

  /// Get central angle in degrees
  double getCentralAngle() {
    return 360.0 / sides;
  }

  /// Calculate apothem (distance from center to middle of any side)
  double getApothem() {
    return sideLength / (2 * math.tan(math.pi / sides));
  }

  /// Calculate circumradius (distance from center to any vertex)
  double getCircumradius() {
    return sideLength / (2 * math.sin(math.pi / sides));
  }

  RegularPolygon copyWith({
    int? sides,
    double? sideLength,
    String? name,
    DateTime? createdAt,
  }) {
    return RegularPolygon(
      sides: sides ?? this.sides,
      sideLength: sideLength ?? this.sideLength,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RegularPolygon &&
           other.sides == sides &&
           other.sideLength == sideLength &&
           other.name == name;
  }

  @override
  int get hashCode => Object.hash(sides, sideLength, name);

  @override
  String toString() => 'RegularPolygon(sides: $sides, sideLength: $sideLength)';
}