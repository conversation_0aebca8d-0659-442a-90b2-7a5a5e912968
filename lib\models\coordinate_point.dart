import 'dart:math' as math;

/// Represents a 2D coordinate point
class CoordinatePoint {
  final double x;
  final double y;

  const CoordinatePoint({required this.x, required this.y});

  /// Create a point from JSON
  factory CoordinatePoint.fromJson(Map<String, dynamic> json) {
    return CoordinatePoint(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );
  }

  /// Convert point to JSON
  Map<String, dynamic> toJson() {
    return {'x': x, 'y': y};
  }

  /// Calculate distance to another point
  double distanceTo(CoordinatePoint other) {
    final dx = x - other.x;
    final dy = y - other.y;
    return math.sqrt(dx * dx + dy * dy);
  }

  /// Calculate angle to another point (in radians)
  double angleTo(CoordinatePoint other) {
    return math.atan2(other.y - y, other.x - x);
  }

  /// Create a copy with modified values
  CoordinatePoint copyWith({double? x, double? y}) {
    return CoordinatePoint(x: x ?? this.x, y: y ?? this.y);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CoordinatePoint && other.x == x && other.y == y;
  }

  @override
  int get hashCode => Object.hash(x, y);

  @override
  String toString() => 'CoordinatePoint(x: $x, y: $y)';
}

/// Represents a polygon defined by coordinate points
class CoordinatePolygon {
  final List<CoordinatePoint> vertices;
  final String name;
  final DateTime createdAt;

  CoordinatePolygon({
    required this.vertices,
    this.name = '',
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Create polygon from JSON
  factory CoordinatePolygon.fromJson(Map<String, dynamic> json) {
    return CoordinatePolygon(
      vertices:
          (json['vertices'] as List)
              .map((v) => CoordinatePoint.fromJson(v))
              .toList(),
      name: json['name'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  /// Convert polygon to JSON
  Map<String, dynamic> toJson() {
    return {
      'vertices': vertices.map((v) => v.toJson()).toList(),
      'name': name,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Check if polygon is valid (at least 3 vertices)
  bool get isValid => vertices.length >= 3;

  /// Get the number of vertices
  int get vertexCount => vertices.length;

  /// Check if polygon is closed (first and last points are the same)
  bool get isClosed {
    if (vertices.length < 2) return false;
    return vertices.first == vertices.last;
  }

  /// Get polygon with ensured closure
  CoordinatePolygon get closed {
    if (isClosed || vertices.length < 3) return this;
    return CoordinatePolygon(
      vertices: [...vertices, vertices.first],
      name: name,
      createdAt: createdAt,
    );
  }

  /// Calculate perimeter of the polygon
  double get perimeter {
    if (vertices.length < 2) return 0.0;

    double total = 0.0;
    for (int i = 0; i < vertices.length - 1; i++) {
      total += vertices[i].distanceTo(vertices[i + 1]);
    }

    // Add distance from last to first if not closed
    if (!isClosed && vertices.length > 2) {
      total += vertices.last.distanceTo(vertices.first);
    }

    return total;
  }

  /// Get bounding box of the polygon
  ({double minX, double maxX, double minY, double maxY}) get boundingBox {
    if (vertices.isEmpty) {
      return (minX: 0.0, maxX: 0.0, minY: 0.0, maxY: 0.0);
    }

    double minX = vertices.first.x;
    double maxX = vertices.first.x;
    double minY = vertices.first.y;
    double maxY = vertices.first.y;

    for (final vertex in vertices) {
      minX = math.min(minX, vertex.x);
      maxX = math.max(maxX, vertex.x);
      minY = math.min(minY, vertex.y);
      maxY = math.max(maxY, vertex.y);
    }

    return (minX: minX, maxX: maxX, minY: minY, maxY: maxY);
  }

  /// Get center point of the polygon
  CoordinatePoint get center {
    if (vertices.isEmpty) return const CoordinatePoint(x: 0, y: 0);

    double sumX = 0.0;
    double sumY = 0.0;

    for (final vertex in vertices) {
      sumX += vertex.x;
      sumY += vertex.y;
    }

    return CoordinatePoint(
      x: sumX / vertices.length,
      y: sumY / vertices.length,
    );
  }

  /// Add a vertex to the polygon
  CoordinatePolygon addVertex(CoordinatePoint point) {
    return CoordinatePolygon(
      vertices: [...vertices, point],
      name: name,
      createdAt: createdAt,
    );
  }

  /// Remove a vertex at the specified index
  CoordinatePolygon removeVertexAt(int index) {
    if (index < 0 || index >= vertices.length) return this;

    final newVertices = List<CoordinatePoint>.from(vertices);
    newVertices.removeAt(index);

    return CoordinatePolygon(
      vertices: newVertices,
      name: name,
      createdAt: createdAt,
    );
  }

  /// Update a vertex at the specified index
  CoordinatePolygon updateVertexAt(int index, CoordinatePoint point) {
    if (index < 0 || index >= vertices.length) return this;

    final newVertices = List<CoordinatePoint>.from(vertices);
    newVertices[index] = point;

    return CoordinatePolygon(
      vertices: newVertices,
      name: name,
      createdAt: createdAt,
    );
  }

  /// Create a copy with modified values
  CoordinatePolygon copyWith({
    List<CoordinatePoint>? vertices,
    String? name,
    DateTime? createdAt,
  }) {
    return CoordinatePolygon(
      vertices: vertices ?? this.vertices,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CoordinatePolygon &&
        other.vertices.length == vertices.length &&
        other.name == name &&
        other.vertices.every((v) => vertices.contains(v));
  }

  @override
  int get hashCode => Object.hash(vertices, name);

  @override
  String toString() =>
      'CoordinatePolygon(vertices: ${vertices.length}, name: $name)';
}
