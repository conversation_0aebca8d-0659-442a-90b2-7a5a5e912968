import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/coordinate_point.dart';
import '../utils/constants.dart';

/// Widget for inputting a single coordinate point
class CoordinateInputWidget extends StatefulWidget {
  final int index;
  final CoordinatePoint point;
  final ValueChanged<CoordinatePoint> onChanged;
  final VoidCallback? onRemove;

  const CoordinateInputWidget({
    super.key,
    required this.index,
    required this.point,
    required this.onChanged,
    this.onRemove,
  });

  @override
  State<CoordinateInputWidget> createState() => _CoordinateInputWidgetState();
}

class _CoordinateInputWidgetState extends State<CoordinateInputWidget> {
  late TextEditingController _xController;
  late TextEditingController _yController;
  late FocusNode _xFocusNode;
  late FocusNode _yFocusNode;

  @override
  void initState() {
    super.initState();
    _xController = TextEditingController(
      text: widget.point.x == 0 ? '' : widget.point.x.toString(),
    );
    _yController = TextEditingController(
      text: widget.point.y == 0 ? '' : widget.point.y.toString(),
    );
    _xFocusNode = FocusNode();
    _yFocusNode = FocusNode();

    _xController.addListener(_onXChanged);
    _yController.addListener(_onYChanged);

    // Add focus listeners to update UI
    _xFocusNode.addListener(() => setState(() {}));
    _yFocusNode.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _xController.dispose();
    _yController.dispose();
    _xFocusNode.dispose();
    _yFocusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CoordinateInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update controllers if point changed externally
    if (oldWidget.point != widget.point) {
      _xController.text = widget.point.x == 0 ? '' : widget.point.x.toString();
      _yController.text = widget.point.y == 0 ? '' : widget.point.y.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool hasFocus = _xFocusNode.hasFocus || _yFocusNode.hasFocus;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: hasFocus ? 4 : 2,
      color: hasFocus ? Colors.blue[50] : Colors.white,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: hasFocus
              ? Border.all(color: const Color(AppConstants.primaryColor), width: 2)
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Header
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.primaryColor),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      '${widget.index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'نقطه ${widget.index + 1}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.onRemove != null)
                  IconButton(
                    onPressed: widget.onRemove,
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red,
                    tooltip: 'حذف نقطه',
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Coordinate inputs
            Row(
              children: [
                // X coordinate
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مختصات X',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _xFocusNode.hasFocus
                              ? const Color(AppConstants.primaryColor)
                              : Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _xController,
                        focusNode: _xFocusNode,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                          signed: true,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^-?\d*\.?\d*'),
                          ),
                        ],
                        decoration: InputDecoration(
                          hintText: '0.0',
                          prefixIcon: Icon(
                            Icons.east,
                            color: _xFocusNode.hasFocus
                                ? const Color(AppConstants.primaryColor)
                                : Colors.grey[600],
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(AppConstants.primaryColor),
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: _xFocusNode.hasFocus
                              ? Colors.blue[25]
                              : Colors.grey[50],
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        onFieldSubmitted: (_) {
                          _yFocusNode.requestFocus();
                        },
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Y coordinate
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مختصات Y',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _yFocusNode.hasFocus
                              ? const Color(AppConstants.primaryColor)
                              : Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _yController,
                        focusNode: _yFocusNode,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                          signed: true,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^-?\d*\.?\d*'),
                          ),
                        ],
                        decoration: InputDecoration(
                          hintText: '0.0',
                          prefixIcon: Icon(
                            Icons.north,
                            color: _yFocusNode.hasFocus
                                ? const Color(AppConstants.primaryColor)
                                : Colors.grey[600],
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(AppConstants.primaryColor),
                              width: 2,
                            ),
                          ),
                          filled: true,
                          fillColor: _yFocusNode.hasFocus
                              ? Colors.blue[25]
                              : Colors.grey[50],
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Validation info
            if (_hasValidationError())
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_outlined,
                      color: Colors.orange[700],
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _getValidationMessage(),
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onXChanged() {
    final text = _xController.text;
    if (text.isEmpty) {
      _updatePoint(0, widget.point.y);
      return;
    }

    final value = double.tryParse(text);
    if (value != null) {
      _updatePoint(value, widget.point.y);
    }
  }

  void _onYChanged() {
    final text = _yController.text;
    if (text.isEmpty) {
      _updatePoint(widget.point.x, 0);
      return;
    }

    final value = double.tryParse(text);
    if (value != null) {
      _updatePoint(widget.point.x, value);
    }
  }

  void _updatePoint(double x, double y) {
    final newPoint = CoordinatePoint(x: x, y: y);
    if (newPoint != widget.point) {
      widget.onChanged(newPoint);
    }
  }

  bool _hasValidationError() {
    return widget.point.x < AppConstants.minCoordinateValue ||
           widget.point.x > AppConstants.maxCoordinateValue ||
           widget.point.y < AppConstants.minCoordinateValue ||
           widget.point.y > AppConstants.maxCoordinateValue;
  }

  String _getValidationMessage() {
    if (widget.point.x < AppConstants.minCoordinateValue ||
        widget.point.x > AppConstants.maxCoordinateValue) {
      return 'مختصات X باید بین ${AppConstants.minCoordinateValue} و ${AppConstants.maxCoordinateValue} باشد';
    }
    if (widget.point.y < AppConstants.minCoordinateValue ||
        widget.point.y > AppConstants.maxCoordinateValue) {
      return 'مختصات Y باید بین ${AppConstants.minCoordinateValue} و ${AppConstants.maxCoordinateValue} باشد';
    }
    return '';
  }
}
