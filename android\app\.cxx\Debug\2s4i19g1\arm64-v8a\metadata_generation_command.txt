                        -HC:\DEV\MobDev\flutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-D<PERSON><PERSON>OID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\DEV\MobDev\android\SDK\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\DEV\MobDev\android\SDK\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\DEV\MobDev\android\SDK\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\DEV\MobDev\android\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\0.projects\Mobile\Myself Pr\jiribyar\build\app\intermediates\cxx\Debug\2s4i19g1\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\0.projects\Mobile\Myself Pr\jiribyar\build\app\intermediates\cxx\Debug\2s4i19g1\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BC:\0.projects\Mobile\Myself Pr\jiribyar\android\app\.cxx\Debug\2s4i19g1\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2