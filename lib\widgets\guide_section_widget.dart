import 'package:flutter/material.dart';
import 'guide_step_widget.dart';

/// Model class representing a section in the user guide
class GuideSection {
  final String title;
  final IconData icon;
  final String description;
  final List<GuideStep> steps;

  const GuideSection({
    required this.title,
    required this.icon,
    required this.description,
    required this.steps,
  });
}

/// Widget for displaying a guide section with its steps
class GuideSectionWidget extends StatelessWidget {
  final GuideSection section;
  final int currentStep;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final VoidCallback? onFinish;

  const GuideSectionWidget({
    super.key,
    required this.section,
    required this.currentStep,
    this.onPrevious,
    this.onNext,
    this.onFinish,
  });

  @override
  Widget build(BuildContext context) {
    final step = section.steps[currentStep];
    final isLastStep = currentStep == section.steps.length - 1;

    return Column(
      children: [
        // Section header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    section.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      section.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                section.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),

        // Progress indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'مرحله ${currentStep + 1} از ${section.steps.length}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${((currentStep + 1) / section.steps.length * 100).round()}%',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: (currentStep + 1) / section.steps.length,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ),

        // Step content
        Expanded(
          child: GuideStepWidget(step: step),
        ),

        // Navigation controls
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Previous button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: currentStep == 0 ? null : onPrevious,
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('قبلی'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Next/Finish button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: isLastStep ? onFinish : onNext,
                  icon: Icon(isLastStep ? Icons.check : Icons.arrow_forward),
                  label: Text(isLastStep ? 'پایان' : 'بعدی'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
