import 'dart:math' as math;
import '../utils/constants.dart';

/// Service for converting between different area units
class UnitConverter {
  /// Convert area from one unit to another
  static double convertArea({
    required double value,
    required String fromUnit,
    required String toUnit,
  }) {
    if (fromUnit == toUnit) return value;

    // Get conversion factors (relative to square meters)
    final fromFactor = AppConstants.areaUnits[fromUnit];
    final toFactor = AppConstants.areaUnits[toUnit];

    if (fromFactor == null || toFactor == null) {
      throw ArgumentError('Unsupported unit: $fromUnit or $toUnit');
    }

    // Convert to square meters first, then to target unit
    final valueInSquareMeters = value / fromFactor;
    return valueInSquareMeters * toFactor;
  }

  /// Convert linear measurement (for perimeter)
  static double convertLinear({
    required double value,
    required String fromUnit,
    required String toUnit,
  }) {
    if (fromUnit == toUnit) return value;

    // For linear measurements, we use the square root of area conversion factors
    final fromFactor = AppConstants.areaUnits[fromUnit];
    final toFactor = AppConstants.areaUnits[toUnit];

    if (fromFactor == null || toFactor == null) {
      throw ArgumentError('Unsupported unit: $fromUnit or $toUnit');
    }

    // Calculate linear conversion factor
    final linearFromFactor = math.sqrt(fromFactor);
    final linearToFactor = math.sqrt(toFactor);

    // Convert to meters first, then to target unit
    final valueInMeters = value / linearFromFactor;
    return valueInMeters * linearToFactor;
  }

  /// Get all area conversions for a given value
  static Map<String, double> convertToAllUnits({
    required double value,
    required String fromUnit,
  }) {
    final Map<String, double> conversions = {};

    for (final unit in AppConstants.areaUnits.keys) {
      conversions[unit] = convertArea(
        value: value,
        fromUnit: fromUnit,
        toUnit: unit,
      );
    }

    return conversions;
  }

  /// Get formatted area string with unit
  static String formatArea({
    required double value,
    required String unit,
    int precision = 2,
    bool includeUnit = true,
  }) {
    final formattedValue = value.toStringAsFixed(precision);
    if (!includeUnit) return formattedValue;

    final unitName = AppConstants.unitNames[unit] ?? unit;
    return '$formattedValue $unitName';
  }

  /// Get formatted area string in English
  static String formatAreaEnglish({
    required double value,
    required String unit,
    int precision = 2,
    bool includeUnit = true,
  }) {
    final formattedValue = value.toStringAsFixed(precision);
    if (!includeUnit) return formattedValue;

    final unitName = AppConstants.unitNamesEnglish[unit] ?? unit;
    return '$formattedValue $unitName';
  }

  /// Get formatted linear measurement string
  static String formatLinear({
    required double value,
    required String unit,
    int precision = 2,
    bool includeUnit = true,
  }) {
    final formattedValue = value.toStringAsFixed(precision);
    if (!includeUnit) return formattedValue;

    // Convert area unit to linear unit name
    String linearUnitName;
    switch (unit) {
      case 'square_meters':
        linearUnitName = 'متر';
        break;
      case 'square_feet':
        linearUnitName = 'فوت';
        break;
      case 'acres':
        linearUnitName = 'فوت'; // Acres perimeter typically in feet
        break;
      case 'hectares':
        linearUnitName = 'متر';
        break;
      case 'jarib':
        linearUnitName = 'متر';
        break;
      default:
        linearUnitName = unit;
    }

    return '$formattedValue $linearUnitName';
  }

  /// Get the most appropriate unit for display based on value
  static String getOptimalUnit(double areaInSquareMeters) {
    // Very small areas - use square meters
    if (areaInSquareMeters < 100) {
      return 'square_meters';
    }
    // Small to medium areas - use square meters or jarib
    else if (areaInSquareMeters < 10000) {
      return 'square_meters';
    }
    // Medium areas - use jarib or hectares
    else if (areaInSquareMeters < 100000) {
      return 'jarib';
    }
    // Large areas - use hectares
    else {
      return 'hectares';
    }
  }

  /// Validate if a unit is supported
  static bool isValidUnit(String unit) {
    return AppConstants.areaUnits.containsKey(unit);
  }

  /// Get list of all supported units
  static List<String> getSupportedUnits() {
    return AppConstants.areaUnits.keys.toList();
  }

  /// Get unit display name in Persian
  static String getUnitDisplayName(String unit) {
    return AppConstants.unitNames[unit] ?? unit;
  }

  /// Get unit display name in English
  static String getUnitDisplayNameEnglish(String unit) {
    return AppConstants.unitNamesEnglish[unit] ?? unit;
  }

  /// Convert area value to a more readable format
  static ({double value, String unit}) getReadableArea({
    required double value,
    required String fromUnit,
  }) {
    // Convert to square meters first
    final areaInSquareMeters = convertArea(
      value: value,
      fromUnit: fromUnit,
      toUnit: 'square_meters',
    );

    // Find the most appropriate unit
    final optimalUnit = getOptimalUnit(areaInSquareMeters);

    // Convert to optimal unit
    final convertedValue = convertArea(
      value: value,
      fromUnit: fromUnit,
      toUnit: optimalUnit,
    );

    return (value: convertedValue, unit: optimalUnit);
  }

  /// Get conversion factor between two units
  static double getConversionFactor({
    required String fromUnit,
    required String toUnit,
  }) {
    if (fromUnit == toUnit) return 1.0;

    final fromFactor = AppConstants.areaUnits[fromUnit];
    final toFactor = AppConstants.areaUnits[toUnit];

    if (fromFactor == null || toFactor == null) {
      throw ArgumentError('Unsupported unit: $fromUnit or $toUnit');
    }

    return toFactor / fromFactor;
  }

  /// Format number with Persian digits
  static String formatWithPersianDigits(String number) {
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

    String result = number;
    for (int i = 0; i < englishDigits.length; i++) {
      result = result.replaceAll(englishDigits[i], persianDigits[i]);
    }
    return result;
  }

  /// Format area with Persian digits and unit
  static String formatAreaPersian({
    required double value,
    required String unit,
    int precision = 2,
    bool includeUnit = true,
  }) {
    final formatted = formatArea(
      value: value,
      unit: unit,
      precision: precision,
      includeUnit: includeUnit,
    );
    return formatWithPersianDigits(formatted);
  }
}
