import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/constants.dart';
import '../models/coordinate_point.dart';
import '../services/shoelace_calculator.dart';
import '../services/unit_converter.dart';
import '../widgets/coordinate_input_widget.dart';
import '../widgets/polygon_preview_widget.dart';
import '../widgets/notes_input_widget.dart';
import 'results_screen.dart';
import 'user_guide_screen.dart';

/// Screen for coordinate-based area calculation
class CoordinateInputScreen extends ConsumerStatefulWidget {
  const CoordinateInputScreen({super.key});

  @override
  ConsumerState<CoordinateInputScreen> createState() =>
      _CoordinateInputScreenState();
}

class _CoordinateInputScreenState extends ConsumerState<CoordinateInputScreen> {
  final List<CoordinatePoint> _vertices = [];
  String _selectedUnit = AppConstants.defaultUnit;
  bool _isCalculating = false;
  String? _errorMessage;
  String? _notes;

  @override
  void initState() {
    super.initState();
    // Start with 3 empty vertices
    _addVertex();
    _addVertex();
    _addVertex();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('محاسبه با مختصات'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _showHelpDialog,
            icon: const Icon(Icons.help_outline),
            tooltip: 'راهنما سریع',
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserGuideScreen(initialSection: 1),
                ),
              );
            },
            icon: const Icon(Icons.menu_book),
            tooltip: 'راهنمای کامل',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Header with unit selection
            _buildHeader(),

            // Main content
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth > 900) {
                    // Desktop layout - side by side
                    return Row(
                      children: [
                        Expanded(flex: 2, child: _buildInputPanel()),
                        const SizedBox(width: 16),
                        Expanded(flex: 1, child: _buildPreviewPanel()),
                      ],
                    );
                  } else if (constraints.maxWidth > 600) {
                    // Tablet layout - side by side with different ratio
                    return Row(
                      children: [
                        Expanded(flex: 3, child: _buildInputPanel()),
                        const SizedBox(width: 12),
                        Expanded(flex: 2, child: _buildPreviewPanel()),
                      ],
                    );
                  } else {
                    // Mobile layout - stacked with tabs
                    return _buildMobileTabLayout();
                  }
                },
              ),
            ),

            // Notes section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: NotesInputWidget(
                notes: _notes,
                onChanged: (notes) => setState(() => _notes = notes),
              ),
            ),

            // Bottom actions
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: const Color(AppConstants.primaryColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'محاسبه با الگوریتم Shoelace',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'تعداد نقاط: ${_vertices.length}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                // Unit selector
                DropdownButton<String>(
                  value: _selectedUnit,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedUnit = value;
                      });
                    }
                  },
                  items:
                      AppConstants.areaUnits.keys.map((unit) {
                        return DropdownMenuItem(
                          value: unit,
                          child: Text(UnitConverter.getUnitDisplayName(unit)),
                        );
                      }).toList(),
                ),
              ],
            ),

            if (_errorMessage != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInputPanel() {
    return Card(
      margin: const EdgeInsets.only(left: 16, right: 8, bottom: 16),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(AppConstants.primaryColor),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.edit_location, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  'ورود مختصات نقاط',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          // Coordinate inputs
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _vertices.length,
                itemBuilder: (context, index) {
                  return CoordinateInputWidget(
                    index: index,
                    point: _vertices[index],
                    onChanged: (point) => _updateVertex(index, point),
                    onRemove:
                        _vertices.length > 3 ? () => _removeVertex(index) : null,
                  );
                },
              ),
            ),
          ),

          // Add vertex button
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: OutlinedButton.icon(
                onPressed:
                    _vertices.length < AppConstants.maxVertices
                        ? _addVertex
                        : null,
                icon: const Icon(Icons.add),
                label: const Text('افزودن نقطه'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(AppConstants.primaryColor),
                  side: const BorderSide(
                    color: Color(AppConstants.primaryColor),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewPanel() {
    return Card(
      margin: const EdgeInsets.only(left: 8, right: 16, bottom: 16),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(AppConstants.secondaryColor),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.visibility, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  'پیش‌نمایش شکل',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          // Preview
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: PolygonPreviewWidget(vertices: _vertices),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // Clear all button
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _clearAll,
              icon: const Icon(Icons.clear_all),
              label: const Text('پاک کردن همه'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Calculate button
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _canCalculate() ? _calculateArea : null,
              icon:
                  _isCalculating
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.calculate),
              label: Text(_isCalculating ? 'در حال محاسبه...' : 'محاسبه مساحت'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileTabLayout() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: TabBar(
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              indicator: BoxDecoration(
                color: const Color(AppConstants.primaryColor),
                borderRadius: BorderRadius.circular(12),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.edit_location, size: 20),
                  text: 'ورود مختصات',
                ),
                Tab(
                  icon: Icon(Icons.visibility, size: 20),
                  text: 'پیش‌نمایش',
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: TabBarView(
              children: [_buildInputPanel(), _buildPreviewPanel()],
            ),
          ),
        ],
      ),
    );
  }

  void _addVertex() {
    setState(() {
      _vertices.add(const CoordinatePoint(x: 0, y: 0));
      _errorMessage = null;
    });
  }

  void _removeVertex(int index) {
    setState(() {
      _vertices.removeAt(index);
      _errorMessage = null;
    });
  }

  void _updateVertex(int index, CoordinatePoint point) {
    setState(() {
      _vertices[index] = point;
      _errorMessage = null;
    });
  }

  void _clearAll() {
    setState(() {
      _vertices.clear();
      _addVertex();
      _addVertex();
      _addVertex();
      _errorMessage = null;
    });
  }

  bool _canCalculate() {
    return _vertices.length >= 3 &&
        _vertices.every((v) => v.x != 0 || v.y != 0) &&
        !_isCalculating;
  }

  Future<void> _calculateArea() async {
    setState(() {
      _isCalculating = true;
      _errorMessage = null;
    });

    try {
      // Validate vertices
      if (!ShoelaceCalculator.validateVertices(_vertices)) {
        throw Exception('نقاط وارد شده معتبر نیستند');
      }

      // Create polygon
      final polygon = CoordinatePolygon(vertices: _vertices);

      // Calculate result
      final result = ShoelaceCalculator.calculateComplete(
        polygon: polygon,
        unit: _selectedUnit,
        name: 'محاسبه مختصاتی ${DateTime.now().millisecondsSinceEpoch}',
        notes: _notes,
      );

      // Navigate to results
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ResultsScreen(result: result),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('راهنمای ورود مختصات'),
            content: const SingleChildScrollView(
              child: Text(
                'برای استفاده از این روش:\n\n'
                '۱. یک نقطه مبدأ (۰،۰) در گوشه‌ای از زمین انتخاب کنید\n'
                '۲. مختصات هر نقطه را نسبت به مبدأ اندازه‌گیری کنید\n'
                '۳. نقاط را به ترتیب ساعتگرد یا پادساعتگرد وارد کنید\n'
                '۴. حداقل ۳ نقطه و حداکثر ۵۰ نقطه قابل ورود است\n\n'
                'نکته: دقت در اندازه‌گیری مختصات باعث افزایش دقت محاسبه می‌شود.',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('متوجه شدم'),
              ),
            ],
          ),
    );
  }
}
