import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// Model representing a triangle with three sides
class Triangle {
  final double sideA;
  final double sideB;
  final double sideC;
  final String? name;

  const Triangle({
    required this.sideA,
    required this.sideB,
    required this.sideC,
    this.name,
  });

  /// Create triangle from JSON
  factory Triangle.fromJson(Map<String, dynamic> json) {
    return Triangle(
      sideA: (json['side_a'] as num).toDouble(),
      sideB: (json['side_b'] as num).toDouble(),
      sideC: (json['side_c'] as num).toDouble(),
      name: json['name'] as String?,
    );
  }

  /// Convert triangle to JSON
  Map<String, dynamic> toJson() {
    return {
      'side_a': sideA,
      'side_b': sideB,
      'side_c': sideC,
      'name': name,
    };
  }

  /// Check if triangle has valid dimensions
  bool get isValid {
    return sideA > 0 && 
           sideB > 0 && 
           sideC > 0 &&
           (sideA + sideB > sideC) &&
           (sideA + sideC > sideB) &&
           (sideB + sideC > sideA);
  }

  /// Check if triangle is complete (all sides entered)
  bool get isComplete {
    return sideA > 0 && sideB > 0 && sideC > 0;
  }

  /// Get triangle perimeter
  double get perimeter {
    return sideA + sideB + sideC;
  }

  /// Create a copy with updated values
  Triangle copyWith({
    double? sideA,
    double? sideB,
    double? sideC,
    String? name,
  }) {
    return Triangle(
      sideA: sideA ?? this.sideA,
      sideB: sideB ?? this.sideB,
      sideC: sideC ?? this.sideC,
      name: name ?? this.name,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Triangle &&
           other.sideA == sideA &&
           other.sideB == sideB &&
           other.sideC == sideC &&
           other.name == name;
  }

  @override
  int get hashCode {
    return Object.hash(sideA, sideB, sideC, name);
  }

  @override
  String toString() {
    return 'Triangle(sideA: $sideA, sideB: $sideB, sideC: $sideC, name: $name)';
  }
}

/// Model representing a collection of triangles for complex land plots
class MultiTriangleShape {
  final List<Triangle> triangles;
  final String? name;

  const MultiTriangleShape({
    required this.triangles,
    this.name,
  });

  /// Create from JSON
  factory MultiTriangleShape.fromJson(Map<String, dynamic> json) {
    return MultiTriangleShape(
      triangles: (json['triangles'] as List)
          .map((t) => Triangle.fromJson(t as Map<String, dynamic>))
          .toList(),
      name: json['name'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'triangles': triangles.map((t) => t.toJson()).toList(),
      'name': name,
    };
  }

  /// Get total area of all triangles
  double get totalArea {
    double total = 0.0;
    for (final triangle in triangles) {
      if (triangle.isValid) {
        // Calculate using Heron's formula
        final s = triangle.perimeter / 2;
        final area = math.sqrt(s * (s - triangle.sideA) * (s - triangle.sideB) * (s - triangle.sideC));
        total += area;
      }
    }
    return total;
  }

  /// Get total perimeter of all triangles
  double get totalPerimeter {
    return triangles.fold(0.0, (sum, triangle) => sum + triangle.perimeter);
  }

  /// Get count of valid triangles
  int get validTriangleCount {
    return triangles.where((t) => t.isValid).length;
  }

  /// Get count of complete triangles
  int get completeTriangleCount {
    return triangles.where((t) => t.isComplete).length;
  }

  /// Check if all triangles are valid
  bool get allTrianglesValid {
    return triangles.isNotEmpty && triangles.every((t) => t.isValid);
  }

  /// Add a triangle
  MultiTriangleShape addTriangle(Triangle triangle) {
    return MultiTriangleShape(
      triangles: [...triangles, triangle],
      name: name,
    );
  }

  /// Remove triangle at index
  MultiTriangleShape removeTriangleAt(int index) {
    if (index < 0 || index >= triangles.length) return this;
    
    final newTriangles = List<Triangle>.from(triangles);
    newTriangles.removeAt(index);
    
    return MultiTriangleShape(
      triangles: newTriangles,
      name: name,
    );
  }

  /// Update triangle at index
  MultiTriangleShape updateTriangleAt(int index, Triangle triangle) {
    if (index < 0 || index >= triangles.length) return this;
    
    final newTriangles = List<Triangle>.from(triangles);
    newTriangles[index] = triangle;
    
    return MultiTriangleShape(
      triangles: newTriangles,
      name: name,
    );
  }

  /// Create a copy with updated values
  MultiTriangleShape copyWith({
    List<Triangle>? triangles,
    String? name,
  }) {
    return MultiTriangleShape(
      triangles: triangles ?? this.triangles,
      name: name ?? this.name,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MultiTriangleShape &&
           listEquals(other.triangles, triangles) &&
           other.name == name;
  }

  @override
  int get hashCode {
    return Object.hash(triangles, name);
  }

  @override
  String toString() {
    return 'MultiTriangleShape(triangles: ${triangles.length}, name: $name)';
  }
}
