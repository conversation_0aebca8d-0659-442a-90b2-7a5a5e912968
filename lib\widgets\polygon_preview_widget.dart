import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/coordinate_point.dart';
import '../utils/constants.dart';

/// Widget for previewing polygon shape from coordinate points
class PolygonPreviewWidget extends StatelessWidget {
  final List<CoordinatePoint> vertices;
  final Color? strokeColor;
  final Color? fillColor;
  final double strokeWidth;
  final bool showVertexLabels;
  final bool showGrid;

  const PolygonPreviewWidget({
    super.key,
    required this.vertices,
    this.strokeColor,
    this.fillColor,
    this.strokeWidth = 2.0,
    this.showVertexLabels = true,
    this.showGrid = true,
  });

  @override
  Widget build(BuildContext context) {
    if (vertices.length < 2) {
      return _buildEmptyState(context);
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: PolygonPainter(
          vertices: vertices,
          strokeColor: strokeColor ?? const Color(AppConstants.primaryColor),
          fillColor:
              fillColor ??
              const Color(AppConstants.primaryColor).withValues(alpha: 0.1),
          strokeWidth: strokeWidth,
          showVertexLabels: showVertexLabels,
          showGrid: showGrid,
        ),
        child: Container(),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[50],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.timeline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'پیش‌نمایش شکل',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'حداقل ۲ نقطه برای نمایش شکل لازم است',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom painter for drawing polygon
class PolygonPainter extends CustomPainter {
  final List<CoordinatePoint> vertices;
  final Color strokeColor;
  final Color fillColor;
  final double strokeWidth;
  final bool showVertexLabels;
  final bool showGrid;

  PolygonPainter({
    required this.vertices,
    required this.strokeColor,
    required this.fillColor,
    required this.strokeWidth,
    required this.showVertexLabels,
    required this.showGrid,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (vertices.length < 2) return;

    // Calculate bounds and scale
    final bounds = _calculateBounds();
    final scale = _calculateScale(size, bounds);
    final offset = _calculateOffset(size, bounds, scale);

    // Draw grid if enabled
    if (showGrid) {
      _drawGrid(canvas, size, bounds, scale, offset);
    }

    // Draw polygon
    _drawPolygon(canvas, scale, offset);

    // Draw vertices
    _drawVertices(canvas, scale, offset);

    // Draw vertex labels if enabled
    if (showVertexLabels) {
      _drawVertexLabels(canvas, scale, offset);
    }
  }

  ({double minX, double maxX, double minY, double maxY}) _calculateBounds() {
    if (vertices.isEmpty) {
      return (minX: 0, maxX: 0, minY: 0, maxY: 0);
    }

    double minX = vertices.first.x;
    double maxX = vertices.first.x;
    double minY = vertices.first.y;
    double maxY = vertices.first.y;

    for (final vertex in vertices) {
      minX = math.min(minX, vertex.x);
      maxX = math.max(maxX, vertex.x);
      minY = math.min(minY, vertex.y);
      maxY = math.max(maxY, vertex.y);
    }

    // Add padding
    final paddingX = (maxX - minX) * 0.1;
    final paddingY = (maxY - minY) * 0.1;

    return (
      minX: minX - paddingX,
      maxX: maxX + paddingX,
      minY: minY - paddingY,
      maxY: maxY + paddingY,
    );
  }

  double _calculateScale(
    Size size,
    ({double minX, double maxX, double minY, double maxY}) bounds,
  ) {
    final dataWidth = bounds.maxX - bounds.minX;
    final dataHeight = bounds.maxY - bounds.minY;

    if (dataWidth == 0 || dataHeight == 0) return 1.0;

    final scaleX = (size.width - 40) / dataWidth;
    final scaleY = (size.height - 40) / dataHeight;

    return math.min(scaleX, scaleY);
  }

  Offset _calculateOffset(
    Size size,
    ({double minX, double maxX, double minY, double maxY}) bounds,
    double scale,
  ) {
    final dataWidth = bounds.maxX - bounds.minX;
    final dataHeight = bounds.maxY - bounds.minY;

    final scaledWidth = dataWidth * scale;
    final scaledHeight = dataHeight * scale;

    final offsetX = (size.width - scaledWidth) / 2 - bounds.minX * scale;
    final offsetY = (size.height - scaledHeight) / 2 + bounds.maxY * scale;

    return Offset(offsetX, offsetY);
  }

  void _drawGrid(
    Canvas canvas,
    Size size,
    ({double minX, double maxX, double minY, double maxY}) bounds,
    double scale,
    Offset offset,
  ) {
    final paint =
        Paint()
          ..color = Colors.grey[300]!
          ..strokeWidth = 0.5;

    // Calculate grid spacing
    final dataWidth = bounds.maxX - bounds.minX;
    final dataHeight = bounds.maxY - bounds.minY;

    final gridSpacingX = _calculateGridSpacing(dataWidth);
    final gridSpacingY = _calculateGridSpacing(dataHeight);

    // Draw vertical lines
    double x = (bounds.minX / gridSpacingX).ceil() * gridSpacingX;
    while (x <= bounds.maxX) {
      final screenX = x * scale + offset.dx;
      if (screenX >= 0 && screenX <= size.width) {
        canvas.drawLine(
          Offset(screenX, 0),
          Offset(screenX, size.height),
          paint,
        );
      }
      x += gridSpacingX;
    }

    // Draw horizontal lines
    double y = (bounds.minY / gridSpacingY).ceil() * gridSpacingY;
    while (y <= bounds.maxY) {
      final screenY = offset.dy - y * scale;
      if (screenY >= 0 && screenY <= size.height) {
        canvas.drawLine(Offset(0, screenY), Offset(size.width, screenY), paint);
      }
      y += gridSpacingY;
    }
  }

  double _calculateGridSpacing(double range) {
    if (range <= 0) return 1.0;

    final magnitude = math.pow(10, (math.log(range) / math.ln10).floor());
    final normalized = range / magnitude;

    if (normalized <= 1) return magnitude * 0.2;
    if (normalized <= 2) return magnitude * 0.5;
    if (normalized <= 5) return magnitude * 1.0;
    return magnitude * 2.0;
  }

  void _drawPolygon(Canvas canvas, double scale, Offset offset) {
    if (vertices.length < 2) return;

    final path = Path();

    // Move to first vertex
    final firstPoint = _transformPoint(vertices.first, scale, offset);
    path.moveTo(firstPoint.dx, firstPoint.dy);

    // Draw lines to other vertices
    for (int i = 1; i < vertices.length; i++) {
      final point = _transformPoint(vertices[i], scale, offset);
      path.lineTo(point.dx, point.dy);
    }

    // Close polygon if we have at least 3 vertices
    if (vertices.length >= 3) {
      path.close();
    }

    // Fill polygon
    if (vertices.length >= 3) {
      final fillPaint =
          Paint()
            ..color = fillColor
            ..style = PaintingStyle.fill;
      canvas.drawPath(path, fillPaint);
    }

    // Stroke polygon
    final strokePaint =
        Paint()
          ..color = strokeColor
          ..strokeWidth = strokeWidth
          ..style = PaintingStyle.stroke;
    canvas.drawPath(path, strokePaint);
  }

  void _drawVertices(Canvas canvas, double scale, Offset offset) {
    final paint =
        Paint()
          ..color = strokeColor
          ..style = PaintingStyle.fill;

    for (final vertex in vertices) {
      final point = _transformPoint(vertex, scale, offset);
      canvas.drawCircle(point, 4, paint);
    }
  }

  void _drawVertexLabels(Canvas canvas, double scale, Offset offset) {
    for (int i = 0; i < vertices.length; i++) {
      final vertex = vertices[i];
      final point = _transformPoint(vertex, scale, offset);

      final textPainter = TextPainter(
        text: TextSpan(
          text: '${i + 1}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Draw background circle
      final backgroundPaint =
          Paint()
            ..color = strokeColor
            ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(point.dx, point.dy - 15), 10, backgroundPaint);

      // Draw text
      textPainter.paint(
        canvas,
        Offset(
          point.dx - textPainter.width / 2,
          point.dy - 15 - textPainter.height / 2,
        ),
      );
    }
  }

  Offset _transformPoint(CoordinatePoint point, double scale, Offset offset) {
    return Offset(point.x * scale + offset.dx, offset.dy - point.y * scale);
  }

  @override
  bool shouldRepaint(PolygonPainter oldDelegate) {
    return oldDelegate.vertices != vertices ||
        oldDelegate.strokeColor != strokeColor ||
        oldDelegate.fillColor != fillColor ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.showVertexLabels != showVertexLabels ||
        oldDelegate.showGrid != showGrid;
  }
}
