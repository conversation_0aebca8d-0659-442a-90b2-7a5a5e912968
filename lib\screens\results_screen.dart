import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculation_result.dart';
import '../services/unit_converter.dart';
import '../services/calculation_history_service.dart';
import '../utils/constants.dart';

/// Screen for displaying calculation results
class ResultsScreen extends ConsumerWidget {
  final CalculationResult result;

  const ResultsScreen({super.key, required this.result});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نتایج محاسبه'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _shareResults(context),
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Main result card
              _buildMainResultCard(context),

              const SizedBox(height: 16),

              // Unit conversions
              _buildUnitConversionsCard(context),

              const SizedBox(height: 16),

              // Calculation details
              _buildCalculationDetailsCard(context),

              const SizedBox(height: 16),

              // Actions
              _buildActionsCard(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainResultCard(BuildContext context) {
    return Card(
      elevation: 6,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.check_circle,
              size: 48,
              color: const Color(AppConstants.primaryColor),
            ),
            const SizedBox(height: 16),
            const Text(
              'محاسبه با موفقیت انجام شد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),

            // Area result
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(
                  AppConstants.primaryColor,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'مساحت محاسبه شده',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    UnitConverter.formatAreaPersian(
                      value: result.area,
                      unit: result.unit,
                      precision: 2,
                    ),
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Color(AppConstants.primaryColor),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Perimeter result
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(
                  AppConstants.secondaryColor,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'محیط محاسبه شده',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    UnitConverter.formatLinear(
                      value: result.perimeter,
                      unit: result.unit,
                      precision: 2,
                    ),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(AppConstants.secondaryColor),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitConversionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.swap_horiz, color: Color(AppConstants.primaryColor)),
                SizedBox(width: 8),
                Text(
                  'تبدیل واحدها',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Unit conversion list
            ...AppConstants.areaUnits.keys.map((unit) {
              final convertedArea = UnitConverter.convertArea(
                value: result.area,
                fromUnit: result.unit,
                toUnit: unit,
              );

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      UnitConverter.getUnitDisplayName(unit),
                      style: const TextStyle(fontSize: 14),
                    ),
                    Text(
                      UnitConverter.formatAreaPersian(
                        value: convertedArea,
                        unit: unit,
                        precision: unit == 'square_meters' ? 2 : 4,
                      ),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationDetailsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Color(AppConstants.primaryColor),
                ),
                SizedBox(width: 8),
                Text(
                  'جزئیات محاسبه',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildDetailRow('روش محاسبه', result.method.displayName),
            _buildDetailRow(
              'زمان محاسبه',
              _formatDateTime(result.calculatedAt),
            ),
            if (result.name != null)
              _buildDetailRow('نام محاسبه', result.name!),
            if (result.notes != null && result.notes!.isNotEmpty)
              _buildNotesRow(result.notes!),

            if (result.isCoordinateBased && result.polygon != null) ...[
              _buildDetailRow(
                'تعداد نقاط',
                '${result.polygon!.vertices.length}',
              ),
              _buildDetailRow(
                'نوع شکل',
                result.polygon!.vertices.length == 3
                    ? 'مثلث'
                    : result.polygon!.vertices.length == 4
                    ? 'چهارضلعی'
                    : 'چندضلعی',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesRow(String notes) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'یادداشت:',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.note,
                  size: 16,
                  color: Colors.blue[700],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    notes,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue[800],
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'عملیات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _saveToHistory(context),
                    icon: const Icon(Icons.save),
                    label: const Text('ذخیره در تاریخچه'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _newCalculation(context),
                    icon: const Icon(Icons.add),
                    label: const Text('محاسبه جدید'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _shareResults(BuildContext context) {
    // TODO: Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('قابلیت اشتراک‌گذاری به زودی اضافه خواهد شد'),
      ),
    );
  }

  Future<void> _saveToHistory(BuildContext context) async {
    try {
      final success = await CalculationHistoryService.saveCalculation(result);

      if (success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('نتیجه در تاریخچه ذخیره شد'),
              backgroundColor: Color(AppConstants.primaryColor),
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('خطا در ذخیره نتیجه'),
              backgroundColor: Color(AppConstants.errorColor),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطا در ذخیره نتیجه'),
            backgroundColor: Color(AppConstants.errorColor),
          ),
        );
      }
    }
  }

  void _newCalculation(BuildContext context) {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
