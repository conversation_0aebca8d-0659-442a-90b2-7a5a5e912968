import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/constants.dart';
import '../models/calculation_result.dart';
import '../models/triangle.dart';
import '../services/geometric_calculator.dart';
import '../services/unit_converter.dart';
import '../widgets/responsive_layout.dart';
import '../widgets/triangle_input_widget.dart';
import '../widgets/multi_triangle_input_widget.dart';
import '../widgets/shape_preview_widget.dart';
import '../widgets/notes_input_widget.dart';
import 'results_screen.dart';
import 'user_guide_screen.dart';

/// Screen for traditional area calculation (sides and angles)
class TraditionalInputScreen extends ConsumerStatefulWidget {
  const TraditionalInputScreen({super.key});

  @override
  ConsumerState<TraditionalInputScreen> createState() =>
      _TraditionalInputScreenState();
}

class _TraditionalInputScreenState
    extends ConsumerState<TraditionalInputScreen> {
  String _selectedShape = 'triangle';
  String _selectedUnit = AppConstants.defaultUnit;
  bool _isCalculating = false;
  String? _errorMessage;
  String? _notes;

  // Triangle inputs
  double _sideA = 0.0;
  double _sideB = 0.0;
  double _sideC = 0.0;

  // Multi-triangle inputs
  MultiTriangleShape _multiTriangleShape = const MultiTriangleShape(
    triangles: [Triangle(sideA: 0, sideB: 0, sideC: 0)],
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('محاسبه سنتی'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _showHelpDialog,
            icon: const Icon(Icons.help_outline),
            tooltip: 'راهنما سریع',
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserGuideScreen(initialSection: 2),
                ),
              );
            },
            icon: const Icon(Icons.menu_book),
            tooltip: 'راهنمای کامل',
          ),
        ],
      ),
      body: SafeArea(
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildShapeSelector(),
          const SizedBox(height: 20),
          _buildInputSection(),
          const SizedBox(height: 20),
          NotesInputWidget(
            notes: _notes,
            onChanged: (notes) => setState(() => _notes = notes),
          ),
          const SizedBox(height: 20),
          _buildCalculateButton(),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildShapeSelector(),
                      const SizedBox(height: 20),
                      _buildInputSection(),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(flex: 1, child: _buildPreviewSection()),
              ],
            ),
          ),
          const SizedBox(height: 20),
          NotesInputWidget(
            notes: _notes,
            onChanged: (notes) => setState(() => _notes = notes),
          ),
          const SizedBox(height: 20),
          _buildCalculateButton(),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: 32),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildShapeSelector(),
                      const SizedBox(height: 24),
                      _buildInputSection(),
                    ],
                  ),
                ),
                const SizedBox(width: 32),
                Expanded(flex: 1, child: _buildPreviewSection()),
              ],
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: 400,
            child: NotesInputWidget(
              notes: _notes,
              onChanged: (notes) => setState(() => _notes = notes),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(width: 400, child: _buildCalculateButton()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.secondaryColor),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.straighten,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'محاسبه سنتی',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'محاسبه مساحت با اضلاع، زوایا و تقسیم به مثلث‌ها',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // Unit selector
                DropdownButton<String>(
                  value: _selectedUnit,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedUnit = value;
                      });
                    }
                  },
                  items:
                      AppConstants.areaUnits.keys.map((unit) {
                        return DropdownMenuItem(
                          value: unit,
                          child: Text(UnitConverter.getUnitDisplayName(unit)),
                        );
                      }).toList(),
                ),
              ],
            ),

            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildShapeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'انتخاب شکل هندسی',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Shape selection buttons
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildShapeButton(
                  'triangle',
                  'مثلث',
                  Icons.change_history,
                  'فرمول هرون',
                ),
                _buildShapeButton(
                  'multi_triangle',
                  'چند مثلث',
                  Icons.auto_awesome_mosaic,
                  'زمین‌های نامنظم',
                ),
                _buildShapeButton(
                  'rectangle',
                  'مستطیل',
                  Icons.crop_din,
                  'طول × عرض',
                ),
                _buildShapeButton(
                  'regular_polygon',
                  'چندضلعی منظم',
                  Icons.hexagon_outlined,
                  'فرمول عمومی',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShapeButton(
    String shapeType,
    String title,
    IconData icon,
    String subtitle,
  ) {
    final isSelected = _selectedShape == shapeType;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedShape = shapeType;
          _errorMessage = null;
          // Reset inputs when shape changes
          _sideA = 0.0;
          _sideB = 0.0;
          _sideC = 0.0;
          _multiTriangleShape = const MultiTriangleShape(
            triangles: [Triangle(sideA: 0, sideB: 0, sideC: 0)],
          );
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                isSelected
                    ? const Color(AppConstants.primaryColor)
                    : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color:
              isSelected
                  ? const Color(
                    AppConstants.primaryColor,
                  ).withValues(alpha: 0.1)
                  : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color:
                  isSelected
                      ? const Color(AppConstants.primaryColor)
                      : Colors.grey[600],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color:
                    isSelected
                        ? const Color(AppConstants.primaryColor)
                        : Colors.grey[800],
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputSection() {
    switch (_selectedShape) {
      case 'triangle':
        return TriangleInputWidget(
          sideA: _sideA,
          sideB: _sideB,
          sideC: _sideC,
          onSideAChanged: (value) => setState(() => _sideA = value),
          onSideBChanged: (value) => setState(() => _sideB = value),
          onSideCChanged: (value) => setState(() => _sideC = value),
        );
      case 'multi_triangle':
        return MultiTriangleInputWidget(
          shape: _multiTriangleShape,
          onChanged: (shape) => setState(() => _multiTriangleShape = shape),
        );
      case 'rectangle':
        return _buildRectangleInput();
      case 'regular_polygon':
        return _buildPolygonInput();
      default:
        return const Card(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Text('لطفاً یک شکل انتخاب کنید'),
          ),
        );
    }
  }

  Widget _buildRectangleInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ورود ابعاد مستطیل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text('این بخش به زودی اضافه خواهد شد'),
          ],
        ),
      ),
    );
  }

  Widget _buildPolygonInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ورود مشخصات چندضلعی منظم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text('این بخش به زودی اضافه خواهد شد'),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.visibility, color: Color(AppConstants.primaryColor)),
                SizedBox(width: 8),
                Text(
                  'پیش‌نمایش شکل',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: ShapePreviewWidget(
                shapeType: _selectedShape,
                parameters: _getShapeParameters(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculateButton() {
    return ElevatedButton.icon(
      onPressed: _canCalculate() ? _calculateArea : null,
      icon:
          _isCalculating
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Icon(Icons.calculate),
      label: Text(
        _isCalculating ? 'در حال محاسبه...' : 'محاسبه مساحت',
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Map<String, double> _getShapeParameters() {
    switch (_selectedShape) {
      case 'triangle':
        return {'side_a': _sideA, 'side_b': _sideB, 'side_c': _sideC};
      case 'multi_triangle':
        return {'triangle_count': _multiTriangleShape.triangles.length.toDouble()};
      case 'rectangle':
        return {'length': _sideA, 'width': _sideB};
      case 'regular_polygon':
        return {'sides': _sideA, 'side_length': _sideB};
      default:
        return {};
    }
  }

  bool _canCalculate() {
    if (_isCalculating) return false;

    switch (_selectedShape) {
      case 'triangle':
        return GeometricCalculator.validateTriangleInputs(
          _sideA,
          _sideB,
          _sideC,
        );
      case 'multi_triangle':
        return GeometricCalculator.validateMultiTriangleInputs(_multiTriangleShape);
      case 'rectangle':
        return _sideA > 0 && _sideB > 0;
      case 'regular_polygon':
        return _sideA >= 3 && _sideB > 0;
      default:
        return false;
    }
  }

  Future<void> _calculateArea() async {
    setState(() {
      _isCalculating = true;
      _errorMessage = null;
    });

    try {
      late final CalculationResult result;

      switch (_selectedShape) {
        case 'triangle':
          result = GeometricCalculator.calculateTriangleComplete(
            sideA: _sideA,
            sideB: _sideB,
            sideC: _sideC,
            unit: _selectedUnit,
            name: 'محاسبه مثلث ${DateTime.now().millisecondsSinceEpoch}',
            notes: _notes,
          );
          break;
        case 'multi_triangle':
          result = GeometricCalculator.calculateMultiTriangleComplete(
            shape: _multiTriangleShape,
            unit: _selectedUnit,
            name: 'محاسبه زمین نامنظم (${_multiTriangleShape.validTriangleCount} مثلث)',
            notes: _notes,
          );
          break;
        case 'rectangle':
          result = GeometricCalculator.calculateRectangleComplete(
            length: _sideA,
            width: _sideB,
            unit: _selectedUnit,
            name: 'محاسبه مستطیل ${DateTime.now().millisecondsSinceEpoch}',
            notes: _notes,
          );
          break;
        default:
          throw Exception('شکل انتخاب شده پشتیبانی نمی‌شود');
      }

      // Navigate to results
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ResultsScreen(result: result),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('راهنمای محاسبه سنتی'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'فرمول هرون (مثلث):',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• برای محاسبه مساحت مثلث با سه ضلع\n'
                    '• فرمول: √(s(s-a)(s-b)(s-c)) که s نیم‌محیط است\n'
                    '• دقت بالا و قابل اعتماد\n'
                    '• نیاز به طول هر سه ضلع',
                  ),
                  SizedBox(height: 16),
                  Text(
                    'چند مثلث (زمین‌های نامنظم):',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• برای زمین‌های پیچیده و نامنظم\n'
                    '• زمین را به چندین مثلث تقسیم کنید\n'
                    '• مساحت کل = جمع مساحت همه مثلث‌ها\n'
                    '• دقت بالا برای اشکال پیچیده\n'
                    '• امکان افزودن تا ۲۰ مثلث',
                  ),
                  SizedBox(height: 16),
                  Text(
                    'مستطیل:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• فرمول ساده: طول × عرض\n'
                    '• مناسب برای زمین‌های مستطیلی شکل\n'
                    '• نیاز به طول و عرض',
                  ),
                  SizedBox(height: 16),
                  Text(
                    'چندضلعی منظم:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• برای اشکال منظم با اضلاع مساوی\n'
                    '• نیاز به تعداد اضلاع و طول هر ضلع\n'
                    '• مناسب برای زمین‌های هندسی منظم',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('متوجه شدم'),
              ),
            ],
          ),
    );
  }
}
