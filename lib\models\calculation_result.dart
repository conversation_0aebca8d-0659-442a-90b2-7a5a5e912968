import 'coordinate_point.dart';

/// Enumeration for calculation methods
enum CalculationMethod {
  coordinate('coordinate', 'روش مختصات (Shoelace)'),
  traditional('traditional', 'روش سنتی (اضلاع و زوایا)');

  const CalculationMethod(this.id, this.displayName);

  final String id;
  final String displayName;
}

/// Represents the result of an area calculation
class CalculationResult {
  final double area;
  final double perimeter;
  final String unit;
  final CalculationMethod method;
  final DateTime calculatedAt;
  final String? name;
  final String? notes;
  final Map<String, dynamic> inputData;
  final Map<String, double>? areaInAllUnits;

  CalculationResult({
    required this.area,
    required this.perimeter,
    required this.unit,
    required this.method,
    required this.inputData,
    DateTime? calculatedAt,
    this.name,
    this.notes,
    this.areaInAllUnits,
  }) : calculatedAt = calculatedAt ?? DateTime.now();

  /// Create result from JSON
  factory CalculationResult.fromJson(Map<String, dynamic> json) {
    return CalculationResult(
      area: (json['area'] as num).toDouble(),
      perimeter: (json['perimeter'] as num).toDouble(),
      unit: json['unit'],
      method: CalculationMethod.values.firstWhere(
        (m) => m.id == json['method'],
        orElse: () => CalculationMethod.coordinate,
      ),
      calculatedAt: DateTime.parse(json['calculatedAt']),
      name: json['name'],
      notes: json['notes'],
      inputData: Map<String, dynamic>.from(json['inputData']),
      areaInAllUnits:
          json['areaInAllUnits'] != null
              ? Map<String, double>.from(json['areaInAllUnits'])
              : null,
    );
  }

  /// Convert result to JSON
  Map<String, dynamic> toJson() {
    return {
      'area': area,
      'perimeter': perimeter,
      'unit': unit,
      'method': method.id,
      'calculatedAt': calculatedAt.toIso8601String(),
      'name': name,
      'notes': notes,
      'inputData': inputData,
      'areaInAllUnits': areaInAllUnits,
    };
  }

  /// Get formatted area string
  String getFormattedArea({int precision = 2}) {
    return area.toStringAsFixed(precision);
  }

  /// Get formatted perimeter string
  String getFormattedPerimeter({int precision = 2}) {
    return perimeter.toStringAsFixed(precision);
  }

  /// Get area in a specific unit
  double getAreaInUnit(String targetUnit, Map<String, double> conversionRates) {
    if (targetUnit == unit) return area;

    // Convert to square meters first, then to target unit
    final areaInSquareMeters = area / conversionRates[unit]!;
    return areaInSquareMeters * conversionRates[targetUnit]!;
  }

  /// Get perimeter in a specific unit (assuming linear conversion)
  double getPerimeterInUnit(
    String targetUnit,
    Map<String, double> conversionRates,
  ) {
    if (targetUnit == unit) return perimeter;

    // For perimeter, we need square root of area conversion factor
    final linearConversionFactor =
        (conversionRates[targetUnit]! / conversionRates[unit]!).abs();
    return perimeter * linearConversionFactor;
  }

  /// Check if this is a coordinate-based calculation
  bool get isCoordinateBased => method == CalculationMethod.coordinate;

  /// Check if this is a traditional calculation
  bool get isTraditionalBased => method == CalculationMethod.traditional;

  /// Get the polygon from input data (for coordinate method)
  CoordinatePolygon? get polygon {
    if (!isCoordinateBased) return null;

    try {
      return CoordinatePolygon.fromJson(inputData['polygon']);
    } catch (e) {
      return null;
    }
  }

  /// Get shape information for traditional method
  Map<String, dynamic>? get shapeInfo {
    if (!isTraditionalBased) return null;
    return inputData['shape'];
  }

  /// Create a copy with modified values
  CalculationResult copyWith({
    double? area,
    double? perimeter,
    String? unit,
    CalculationMethod? method,
    DateTime? calculatedAt,
    String? name,
    String? notes,
    Map<String, dynamic>? inputData,
    Map<String, double>? areaInAllUnits,
  }) {
    return CalculationResult(
      area: area ?? this.area,
      perimeter: perimeter ?? this.perimeter,
      unit: unit ?? this.unit,
      method: method ?? this.method,
      calculatedAt: calculatedAt ?? this.calculatedAt,
      name: name ?? this.name,
      notes: notes ?? this.notes,
      inputData: inputData ?? this.inputData,
      areaInAllUnits: areaInAllUnits ?? this.areaInAllUnits,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalculationResult &&
        other.area == area &&
        other.perimeter == perimeter &&
        other.unit == unit &&
        other.method == method &&
        other.calculatedAt == calculatedAt &&
        other.name == name &&
        other.notes == notes;
  }

  @override
  int get hashCode =>
      Object.hash(area, perimeter, unit, method, calculatedAt, name, notes);

  @override
  String toString() =>
      'CalculationResult(area: $area $unit, method: ${method.displayName})';
}

/// Represents calculation history
class CalculationHistory {
  final List<CalculationResult> results;

  const CalculationHistory({required this.results});

  /// Create history from JSON
  factory CalculationHistory.fromJson(Map<String, dynamic> json) {
    return CalculationHistory(
      results:
          (json['results'] as List)
              .map((r) => CalculationResult.fromJson(r))
              .toList(),
    );
  }

  /// Convert history to JSON
  Map<String, dynamic> toJson() {
    return {'results': results.map((r) => r.toJson()).toList()};
  }

  /// Add a new result to history
  CalculationHistory addResult(CalculationResult result) {
    return CalculationHistory(results: [result, ...results]);
  }

  /// Remove a result at the specified index
  CalculationHistory removeResultAt(int index) {
    if (index < 0 || index >= results.length) return this;

    final newResults = List<CalculationResult>.from(results);
    newResults.removeAt(index);

    return CalculationHistory(results: newResults);
  }

  /// Clear all results
  CalculationHistory clear() {
    return const CalculationHistory(results: []);
  }

  /// Get results by method
  List<CalculationResult> getResultsByMethod(CalculationMethod method) {
    return results.where((r) => r.method == method).toList();
  }

  /// Get recent results (last n results)
  List<CalculationResult> getRecentResults(int count) {
    return results.take(count).toList();
  }

  /// Check if history is empty
  bool get isEmpty => results.isEmpty;

  /// Get the count of results
  int get length => results.length;

  @override
  String toString() => 'CalculationHistory(${results.length} results)';
}
