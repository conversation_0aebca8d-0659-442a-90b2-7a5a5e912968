import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Widget for inputting optional notes for calculations
class NotesInputWidget extends StatefulWidget {
  final String? notes;
  final ValueChanged<String?> onChanged;
  final bool isExpanded;

  const NotesInputWidget({
    super.key,
    this.notes,
    required this.onChanged,
    this.isExpanded = false,
  });

  @override
  State<NotesInputWidget> createState() => _NotesInputWidgetState();
}

class _NotesInputWidgetState extends State<NotesInputWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.notes ?? '');
    _focusNode = FocusNode();
    _isExpanded = widget.isExpanded;

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(NotesInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.notes != widget.notes) {
      _controller.text = widget.notes ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
                if (_isExpanded) {
                  _focusNode.requestFocus();
                }
              });
            },
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isExpanded || _focusNode.hasFocus
                    ? const Color(AppConstants.primaryColor)
                    : Colors.grey[100],
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(12),
                  topRight: const Radius.circular(12),
                  bottomLeft: _isExpanded ? Radius.zero : const Radius.circular(12),
                  bottomRight: _isExpanded ? Radius.zero : const Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note_add,
                    color: _isExpanded || _focusNode.hasFocus
                        ? Colors.white
                        : Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'یادداشت (اختیاری)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _isExpanded || _focusNode.hasFocus
                            ? Colors.white
                            : Colors.grey[800],
                      ),
                    ),
                  ),
                  if (_controller.text.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _isExpanded || _focusNode.hasFocus
                            ? Colors.white.withValues(alpha: 0.2)
                            : const Color(AppConstants.primaryColor).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${_controller.text.length} کاراکتر',
                        style: TextStyle(
                          fontSize: 12,
                          color: _isExpanded || _focusNode.hasFocus
                              ? Colors.white70
                              : const Color(AppConstants.primaryColor),
                        ),
                      ),
                    ),
                  const SizedBox(width: 8),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: _isExpanded || _focusNode.hasFocus
                        ? Colors.white
                        : Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),

          // Input field
          if (_isExpanded)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توضیحات یا شناسه زمین (مثال: قطعه شماره ۱، زمین کشاورزی شمالی، ملک خانوادگی)',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextFormField(
                    controller: _controller,
                    focusNode: _focusNode,
                    maxLines: 3,
                    maxLength: 200,
                    decoration: InputDecoration(
                      hintText: 'یادداشت خود را اینجا بنویسید...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          color: Color(AppConstants.primaryColor),
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.all(12),
                    ),
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          'این یادداشت در تاریخچه محاسبات ذخیره می‌شود و به شما کمک می‌کند محاسبات را بهتر شناسایی کنید.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[600],
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _onTextChanged() {
    final text = _controller.text.trim();
    widget.onChanged(text.isEmpty ? null : text);
  }
}
