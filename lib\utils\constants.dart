/// Constants for the Jarib-Yar land area calculator app
class AppConstants {
  // App Information
  static const String appName = 'جریب‌یار';
  static const String appNameEnglish = 'Jarib-Yar';
  static const String appDescription = 'محاسبه مساحت زمین‌های نامنظم';

  // Calculation Methods
  static const String coordinateMethod = 'coordinate';
  static const String traditionalMethod = 'traditional';

  // Supported Units
  static const Map<String, double> areaUnits = {
    'square_meters': 1.0,
    'square_feet': 10.764,
    'acres': 0.000247105,
    'hectares': 0.0001,
    'jarib': 0.0005, // Traditional Persian unit (1 jarib ≈ 2000 m²)
  };

  // Unit Display Names (Persian)
  static const Map<String, String> unitNames = {
    'square_meters': 'متر مربع',
    'square_feet': 'فوت مربع',
    'acres': 'جریب انگلیسی',
    'hectares': 'هکتار',
    'jarib': 'جریب',
  };

  // Unit Display Names (English)
  static const Map<String, String> unitNamesEnglish = {
    'square_meters': 'Square Meters',
    'square_feet': 'Square Feet',
    'acres': 'Acres',
    'hectares': 'Hectares',
    'jarib': 'Jarib',
  };

  // Colors
  static const primaryColor = 0xFF2E7D32; // Green
  static const secondaryColor = 0xFF4CAF50; // Light Green
  static const accentColor = 0xFF8BC34A; // Lime
  static const errorColor = 0xFFD32F2F; // Red
  static const warningColor = 0xFFFF9800; // Orange

  // Validation Constants
  static const double minCoordinateValue = -999999.0;
  static const double maxCoordinateValue = 999999.0;
  static const double minSideLength = 0.1;
  static const double maxSideLength = 100000.0;
  static const double minAngle = 0.1;
  static const double maxAngle = 179.9;
  static const int minVertices = 3;
  static const int maxVertices = 50;

  // Default Values
  static const String defaultUnit = 'square_meters';
  static const int defaultPrecision = 2;

  // Storage Keys
  static const String calculationHistoryKey = 'calculation_history';
  static const String preferredUnitKey = 'preferred_unit';
  static const String preferredMethodKey = 'preferred_method';

  // Error Messages (Persian)
  static const Map<String, String> errorMessages = {
    'invalid_coordinate': 'مختصات وارد شده معتبر نیست',
    'invalid_side_length': 'طول ضلع وارد شده معتبر نیست',
    'invalid_angle': 'زاویه وارد شده معتبر نیست',
    'insufficient_vertices': 'حداقل ۳ نقطه مورد نیاز است',
    'too_many_vertices': 'حداکثر ۵۰ نقطه مجاز است',
    'calculation_error': 'خطا در محاسبه مساحت',
    'invalid_polygon': 'چندضلعی وارد شده معتبر نیست',
  };

  // Success Messages (Persian)
  static const Map<String, String> successMessages = {
    'calculation_complete': 'محاسبه با موفقیت انجام شد',
    'data_saved': 'اطلاعات ذخیره شد',
    'history_cleared': 'تاریخچه پاک شد',
  };

  // UI Text (Persian)
  static const Map<String, String> uiText = {
    'coordinate_method': 'روش مختصات (الگوریتم Shoelace)',
    'traditional_method': 'روش سنتی (اضلاع و زوایا)',
    'heron_method': 'فرمول هرون (مثلث)',
    'calculate_area': 'محاسبه مساحت',
    'add_point': 'افزودن نقطه',
    'remove_point': 'حذف نقطه',
    'clear_all': 'پاک کردن همه',
    'results': 'نتایج',
    'history': 'تاریخچه',
    'settings': 'تنظیمات',
    'help': 'راهنما',
    'coordinate_x': 'مختصات X',
    'coordinate_y': 'مختصات Y',
    'side_length': 'طول ضلع',
    'angle': 'زاویه',
    'area': 'مساحت',
    'perimeter': 'محیط',
    'method_used': 'روش محاسبه',
    'precision': 'دقت',
    'unit': 'واحد',
    'side_a': 'ضلع الف',
    'side_b': 'ضلع ب',
    'side_c': 'ضلع ج',
    'triangle': 'مثلث',
    'quadrilateral': 'چهارضلعی',
    'polygon': 'چندضلعی',
  };
}
