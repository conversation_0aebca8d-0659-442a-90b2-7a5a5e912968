import 'package:flutter/material.dart';
import '../models/triangle.dart';
import '../utils/constants.dart';
import 'single_triangle_input_widget.dart';

/// Widget for inputting multiple triangles for complex land plots
class MultiTriangleInputWidget extends StatefulWidget {
  final MultiTriangleShape shape;
  final ValueChanged<MultiTriangleShape> onChanged;

  const MultiTriangleInputWidget({
    super.key,
    required this.shape,
    required this.onChanged,
  });

  @override
  State<MultiTriangleInputWidget> createState() => _MultiTriangleInputWidgetState();
}

class _MultiTriangleInputWidgetState extends State<MultiTriangleInputWidget> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(AppConstants.primaryColor),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.auto_awesome_mosaic,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تقسیم زمین به مثلث‌ها',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'تعداد مثلث‌ها: ${widget.shape.triangles.length}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Content
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                // Instructions
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'برای زمین‌های نامنظم، آن را به چندین مثلث تقسیم کنید و ابعاد هر مثلث را وارد کنید. مساحت کل از جمع مساحت همه مثلث‌ها محاسبه می‌شود.',
                            style: TextStyle(fontSize: 13),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Triangle inputs
                if (widget.shape.triangles.isNotEmpty)
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: widget.shape.triangles.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: SingleTriangleInputWidget(
                          triangle: widget.shape.triangles[index],
                          index: index,
                          onChanged: (triangle) => _updateTriangle(index, triangle),
                          onRemove: widget.shape.triangles.length > 1 
                              ? () => _removeTriangle(index) 
                              : null,
                        ),
                      );
                    },
                  ),

                // Add triangle button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: widget.shape.triangles.length < 20 
                              ? _addTriangle 
                              : null,
                          icon: const Icon(Icons.add),
                          label: const Text('افزودن مثلث'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: const Color(AppConstants.primaryColor),
                            side: const BorderSide(
                              color: Color(AppConstants.primaryColor),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      if (widget.shape.triangles.length > 1) ...[
                        const SizedBox(width: 12),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _clearAll,
                            icon: const Icon(Icons.clear_all),
                            label: const Text('پاک کردن همه'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.red,
                              side: const BorderSide(color: Colors.red),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Summary
                if (widget.shape.triangles.isNotEmpty)
                  _buildSummary(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummary() {
    final validCount = widget.shape.validTriangleCount;
    final totalCount = widget.shape.triangles.length;
    final totalArea = widget.shape.totalArea;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: validCount == totalCount && totalCount > 0
            ? Colors.green[50]
            : Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: validCount == totalCount && totalCount > 0
              ? Colors.green[200]!
              : Colors.orange[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                validCount == totalCount && totalCount > 0
                    ? Icons.check_circle
                    : Icons.warning_amber,
                color: validCount == totalCount && totalCount > 0
                    ? Colors.green[700]
                    : Colors.orange[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'خلاصه محاسبات',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: validCount == totalCount && totalCount > 0
                      ? Colors.green[700]
                      : Colors.orange[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'مثلث‌های معتبر: $validCount از $totalCount',
            style: const TextStyle(fontSize: 13),
          ),
          if (validCount > 0) ...[
            Text(
              'مساحت کل: ${totalArea.toStringAsFixed(2)} متر مربع',
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
          if (validCount < totalCount)
            const Text(
              'لطفاً ابعاد همه مثلث‌ها را به درستی وارد کنید',
              style: TextStyle(fontSize: 12),
            ),
        ],
      ),
    );
  }

  void _addTriangle() {
    final newTriangle = const Triangle(sideA: 0, sideB: 0, sideC: 0);
    final updatedShape = widget.shape.addTriangle(newTriangle);
    widget.onChanged(updatedShape);
  }

  void _removeTriangle(int index) {
    final updatedShape = widget.shape.removeTriangleAt(index);
    widget.onChanged(updatedShape);
  }

  void _updateTriangle(int index, Triangle triangle) {
    final updatedShape = widget.shape.updateTriangleAt(index, triangle);
    widget.onChanged(updatedShape);
  }

  void _clearAll() {
    final updatedShape = MultiTriangleShape(
      triangles: [const Triangle(sideA: 0, sideB: 0, sideC: 0)],
      name: widget.shape.name,
    );
    widget.onChanged(updatedShape);
  }
}
