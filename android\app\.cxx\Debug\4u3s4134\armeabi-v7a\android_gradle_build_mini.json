{"buildFiles": ["C:\\DEV\\MobDev\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\DEV\\MobDev\\android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0.projects\\Mobile\\Myself Pr\\jiribyar\\android\\app\\.cxx\\Debug\\4u3s4134\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\DEV\\MobDev\\android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0.projects\\Mobile\\Myself Pr\\jiribyar\\android\\app\\.cxx\\Debug\\4u3s4134\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}