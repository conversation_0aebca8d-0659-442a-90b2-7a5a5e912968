import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../widgets/guide_step_widget.dart';
import '../widgets/guide_section_widget.dart';

/// Comprehensive user guide screen for the Jarib-Yar application
class UserGuideScreen extends StatefulWidget {
  final int? initialSection;
  
  const UserGuideScreen({super.key, this.initialSection});

  @override
  State<UserGuideScreen> createState() => _UserGuideScreenState();
}

class _UserGuideScreenState extends State<UserGuideScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  int _currentSection = 0;
  int _currentStep = 0;

  final List<GuideSection> _guideSections = [
    GuideSection(
      title: 'شروع کار',
      icon: Icons.play_circle_outline,
      description: 'آشنایی با برنامه و نحوه استفاده',
      steps: _getGettingStartedSteps(),
    ),
    GuideSection(
      title: 'روش مختصات',
      icon: Icons.timeline,
      description: 'محاسبه با الگوریتم Shoelace',
      steps: _getCoordinateMethodSteps(),
    ),
    GuideSection(
      title: 'روش سنتی',
      icon: Icons.straighten,
      description: 'محاسبه با اضلاع و زوایا',
      steps: _getTraditionalMethodSteps(),
    ),
    GuideSection(
      title: 'مدیریت نتایج',
      icon: Icons.assessment,
      description: 'ذخیره و اشتراک‌گذاری نتایج',
      steps: _getResultsManagementSteps(),
    ),
    GuideSection(
      title: 'تاریخچه',
      icon: Icons.history,
      description: 'مشاهده محاسبات قبلی',
      steps: _getHistorySteps(),
    ),
    GuideSection(
      title: 'نکات کاربردی',
      icon: Icons.lightbulb_outline,
      description: 'راهنمایی‌های حرفه‌ای',
      steps: _getTipsSteps(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _currentSection = widget.initialSection ?? 0;
    _tabController = TabController(
      length: _guideSections.length,
      vsync: this,
      initialIndex: _currentSection,
    );
    _pageController = PageController(initialPage: 0);
    
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentSection = _tabController.index;
          _currentStep = 0;
        });
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('راهنمای کاربری'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: _guideSections.map((section) => Tab(
            icon: Icon(section.icon, size: 20),
            text: section.title,
          )).toList(),
        ),
      ),
      body: Column(
        children: [
          // Section header
          _buildSectionHeader(),
          
          // Progress indicator
          _buildProgressIndicator(),
          
          // Content
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              itemCount: _guideSections[_currentSection].steps.length,
              itemBuilder: (context, index) {
                return _buildStepContent(
                  _guideSections[_currentSection].steps[index],
                );
              },
            ),
          ),
          
          // Navigation controls
          _buildNavigationControls(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader() {
    final section = _guideSections[_currentSection];
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(AppConstants.primaryColor).withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                section.icon,
                color: const Color(AppConstants.primaryColor),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  section.title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(AppConstants.primaryColor),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            section.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final totalSteps = _guideSections[_currentSection].steps.length;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'مرحله ${_currentStep + 1} از $totalSteps',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${((_currentStep + 1) / totalSteps * 100).round()}%',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentStep + 1) / totalSteps,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(
              Color(AppConstants.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(GuideStep step) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step title
          Text(
            step.title,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Step content
          if (step.image != null) ...[
            Center(
              child: Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: step.image!,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          Text(
            step.content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
          
          if (step.tips.isNotEmpty) ...[
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.blue,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'نکات مهم:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...step.tips.map((tip) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• ', style: TextStyle(color: Colors.blue)),
                        Expanded(
                          child: Text(
                            tip,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationControls() {
    final totalSteps = _guideSections[_currentSection].steps.length;
    final isFirstStep = _currentStep == 0;
    final isLastStep = _currentStep == totalSteps - 1;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous button
          Expanded(
            child: OutlinedButton.icon(
              onPressed: isFirstStep ? null : _goToPreviousStep,
              icon: const Icon(Icons.arrow_back),
              label: const Text('قبلی'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Next button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: isLastStep ? _finishGuide : _goToNextStep,
              icon: Icon(isLastStep ? Icons.check : Icons.arrow_forward),
              label: Text(isLastStep ? 'پایان' : 'بعدی'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    final totalSteps = _guideSections[_currentSection].steps.length;
    if (_currentStep < totalSteps - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _finishGuide() {
    Navigator.of(context).pop();
  }

  // Static methods for creating guide content
  static List<GuideStep> _getGettingStartedSteps() {
    return [
      GuideStep(
        title: 'خوش آمدید به جریب‌یار',
        content: 'جریب‌یار یک ابزار حرفه‌ای برای محاسبه مساحت زمین‌های نامنظم است که با استفاده از الگوریتم‌های دقیق ریاضی، امکان محاسبه مساحت انواع زمین‌ها را فراهم می‌کند.\n\n'
            'این برنامه برای کشاورزان، متخصصان املاک، مهندسان نقشه‌برداری و هر کسی که نیاز به محاسبه دقیق مساحت زمین دارد، طراحی شده است.',
        image: Container(
          height: 150,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF2E7D32),
                const Color(0xFF4CAF50),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.landscape,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 8),
                Text(
                  'جریب‌یار',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        tips: [
          'برنامه کاملاً رایگان و بدون نیاز به اتصال اینترنت است',
          'تمام محاسبات روی دستگاه شما انجام می‌شود',
          'اطلاعات شما محفوظ و خصوصی باقی می‌ماند',
        ],
      ),
      GuideStep(
        title: 'آشنایی با صفحه اصلی',
        content: 'صفحه اصلی برنامه شامل موارد زیر است:\n\n'
            '• دکمه "شروع محاسبه مساحت" برای شروع محاسبه جدید\n'
            '• آیکون تاریخچه در بالای صفحه برای مشاهده محاسبات قبلی\n'
            '• آیکون اطلاعات برای مشاهده جزئیات برنامه و سازنده\n'
            '• معرفی ویژگی‌های کلیدی برنامه',
        tips: [
          'از دکمه تاریخچه برای دسترسی سریع به محاسبات قبلی استفاده کنید',
          'در صفحه درباره برنامه، اطلاعات تماس سازنده موجود است',
        ],
      ),
      GuideStep(
        title: 'انتخاب روش محاسبه',
        content: 'جریب‌یار دو روش اصلی برای محاسبه مساحت ارائه می‌دهد:\n\n'
            '۱. روش مختصات (Shoelace Algorithm):\n'
            '   • مناسب زمانی که مختصات نقاط زمین را دارید\n'
            '   • دقت بسیار بالا برای اشکال پیچیده\n'
            '   • قابلیت محاسبه تا ۵۰ نقطه\n\n'
            '۲. روش سنتی (فرمول هرون):\n'
            '   • مناسب برای زمین‌هایی که به صورت مثلث تقسیم می‌شوند\n'
            '   • نیاز به اندازه‌گیری اضلاع\n'
            '   • امکان ترکیب چندین مثلث',
        tips: [
          'اگر GPS یا ابزار اندازه‌گیری مختصات دارید، روش مختصات را انتخاب کنید',
          'برای زمین‌های ساده، روش سنتی سریع‌تر است',
          'هر دو روش نتایج دقیق ارائه می‌دهند',
        ],
      ),
    ];
  }

  static List<GuideStep> _getCoordinateMethodSteps() {
    return [
      GuideStep(
        title: 'آماده‌سازی برای ورود مختصات',
        content: 'قبل از شروع ورود مختصات، مراحل زیر را انجام دهید:\n\n'
            '۱. یک نقطه مبدأ (۰،۰) در گوشه‌ای از زمین انتخاب کنید\n'
            '۲. جهت محورهای X و Y را مشخص کنید\n'
            '۳. واحد اندازه‌گیری (متر، فوت و غیره) را تعیین کنید\n'
            '۴. ابزار اندازه‌گیری مناسب (GPS، متر، نوار اندازه‌گیری) تهیه کنید',
        image: Container(
          height: 150,
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: CustomPaint(
            painter: CoordinateSystemPainter(),
            size: const Size(double.infinity, 150),
          ),
        ),
        tips: [
          'نقطه مبدأ را در گوشه‌ای انتخاب کنید که دسترسی آسان داشته باشید',
          'از GPS برای دقت بالا استفاده کنید',
          'محور X معمولاً شرق-غرب و محور Y شمال-جنوب در نظر گرفته می‌شود',
        ],
      ),
      GuideStep(
        title: 'ورود نقاط مختصات',
        content: 'برای ورود مختصات نقاط:\n\n'
            '۱. روی دکمه "افزودن نقطه" کلیک کنید\n'
            '۲. مختصات X و Y هر نقطه را وارد کنید\n'
            '۳. نقاط را به ترتیب ساعتگرد یا پادساعتگرد وارد کنید\n'
            '۴. حداقل ۳ نقطه برای تشکیل یک شکل لازم است\n'
            '۵. از تب "پیش‌نمایش" برای بررسی شکل استفاده کنید',
        tips: [
          'ترتیب ورود نقاط مهم است - همیشه یک جهت را حفظ کنید',
          'از پیش‌نمایش برای اطمینان از صحت شکل استفاده کنید',
          'می‌توانید نقاط اضافی را حذف کنید',
          'دقت در ورود اعداد اعشاری برای نتیجه بهتر',
        ],
      ),
      GuideStep(
        title: 'بررسی و محاسبه',
        content: 'پس از ورود تمام نقاط:\n\n'
            '۱. از تب "پیش‌نمایش" برای بررسی نهایی شکل استفاده کنید\n'
            '۲. واحد مورد نظر برای نتیجه را انتخاب کنید\n'
            '۳. در صورت نیاز، یادداشت اضافه کنید\n'
            '۴. روی دکمه "محاسبه مساحت" کلیک کنید\n\n'
            'الگوریتم Shoelace مساحت را با دقت بالا محاسبه خواهد کرد.',
        tips: [
          'شکل باید بسته باشد (نقطه آخر به اول متصل شود)',
          'اگر شکل درست نیست، نقاط را ویرایش کنید',
          'یادداشت برای شناسایی آینده محاسبه مفید است',
        ],
      ),
    ];
  }

  static List<GuideStep> _getTraditionalMethodSteps() {
    return [
      GuideStep(
        title: 'آشنایی با روش سنتی',
        content: 'روش سنتی بر اساس تقسیم زمین به مثلث‌ها و استفاده از فرمول هرون کار می‌کند:\n\n'
            'فرمول هرون: مساحت = √(s × (s-a) × (s-b) × (s-c))\n'
            'که s = (a+b+c)/2 (نیم محیط)\n\n'
            'این روش برای زمین‌هایی مناسب است که:\n'
            '• به راحتی به مثلث تقسیم می‌شوند\n'
            '• امکان اندازه‌گیری اضلاع وجود دارد\n'
            '• شکل نسبتاً ساده دارند',
        interactiveElement: CodeExample(
          title: 'فرمول هرون',
          code: 'مساحت = √(s × (s-a) × (s-b) × (s-c))\nکه s = (a+b+c)/2',
          explanation: 'a، b، c طول اضلاع مثلث و s نیم محیط مثلث است',
        ),
        tips: [
          'این روش برای مثلث‌های قائم‌الزاویه و منظم بسیار دقیق است',
          'اندازه‌گیری دقیق اضلاع کلید موفقیت است',
          'می‌توانید زمین را به چندین مثلث تقسیم کنید',
        ],
      ),
      GuideStep(
        title: 'ورود اطلاعات مثلث',
        content: 'برای محاسبه مساحت یک مثلث:\n\n'
            '۱. طول هر سه ضلع مثلث را اندازه‌گیری کنید\n'
            '۲. اطلاعات را در فیلدهای مربوطه وارد کنید\n'
            '۳. واحد اندازه‌گیری را انتخاب کنید\n'
            '۴. برنامه خودکار بررسی می‌کند که آیا مثلث معتبر است یا نه\n\n'
            'شرط معتبر بودن مثلث: مجموع هر دو ضلع باید از ضلع سوم بزرگ‌تر باشد.',
        tips: [
          'از متر یا نوار اندازه‌گیری دقیق استفاده کنید',
          'اضلاع را دو بار اندازه‌گیری کنید تا از دقت اطمینان حاصل کنید',
          'اگر مثلث معتبر نیست، پیام خطا نمایش داده می‌شود',
        ],
      ),
      GuideStep(
        title: 'محاسبه چند مثلث',
        content: 'برای زمین‌های پیچیده‌تر:\n\n'
            '۱. زمین را به چندین مثلث تقسیم کنید\n'
            '۲. هر مثلث را جداگانه اندازه‌گیری کنید\n'
            '۳. از دکمه "افزودن مثلث" برای اضافه کردن مثلث‌های بیشتر استفاده کنید\n'
            '۴. برنامه مساحت کل را با جمع مساحت همه مثلث‌ها محاسبه می‌کند\n\n'
            'این روش برای زمین‌های نامنظم بسیار مؤثر است.',
        tips: [
          'سعی کنید مثلث‌ها تا حد امکان منظم باشند',
          'از تداخل مثلث‌ها جلوگیری کنید',
          'می‌توانید تا ۱۰ مثلث اضافه کنید',
          'هر مثلث را با یادداشت مشخص کنید',
        ],
      ),
    ];
  }

  static List<GuideStep> _getResultsManagementSteps() {
    return [
      GuideStep(
        title: 'مشاهده نتایج محاسبه',
        content: 'پس از محاسبه مساحت، صفحه نتایج شامل موارد زیر است:\n\n'
            '• مساحت اصلی با واحد انتخابی\n'
            '• تبدیل خودکار به واحدهای مختلف (متر مربع، جریب، هکتار، فوت مربع)\n'
            '• جزئیات محاسبه شامل روش، زمان و نام\n'
            '• یادداشت‌های اضافه شده\n'
            '• دکمه‌های عملیات (ذخیره، اشتراک‌گذاری، محاسبه جدید)',
        tips: [
          'تمام تبدیل واحدها خودکار انجام می‌شود',
          'می‌توانید نتایج را به صورت متن اشتراک‌گذاری کنید',
          'نتایج خودکار در تاریخچه ذخیره می‌شوند',
        ],
      ),
      GuideStep(
        title: 'ذخیره و نام‌گذاری محاسبات',
        content: 'برای مدیریت بهتر محاسبات:\n\n'
            '۱. قبل از محاسبه، نام مناسبی برای محاسبه انتخاب کنید\n'
            '۲. یادداشت توضیحی اضافه کنید\n'
            '۳. محاسبه خودکار در تاریخچه ذخیره می‌شود\n'
            '۴. از صفحه تاریخچه می‌توانید محاسبات را مدیریت کنید',
        tips: [
          'نام‌های توصیفی انتخاب کنید (مثل "زمین شمالی خانه")',
          'یادداشت‌ها برای شناسایی آینده مفید هستند',
          'تاریخ و زمان خودکار ثبت می‌شود',
        ],
      ),
      GuideStep(
        title: 'اشتراک‌گذاری نتایج',
        content: 'می‌توانید نتایج محاسبه را به روش‌های مختلف اشتراک‌گذاری کنید:\n\n'
            '• ارسال متنی شامل تمام جزئیات\n'
            '• کپی کردن در کلیپ‌بورد\n'
            '• ارسال از طریق پیامک، ایمیل یا شبکه‌های اجتماعی\n'
            '• ذخیره در فایل متنی\n\n'
            'فرمت اشتراک‌گذاری شامل نام، مساحت، واحدها، روش محاسبه و زمان است.',
        tips: [
          'فرمت اشتراک‌گذاری حرفه‌ای و قابل فهم است',
          'می‌توانید متن را قبل از ارسال ویرایش کنید',
          'برای گزارش‌های رسمی مناسب است',
        ],
      ),
    ];
  }

  static List<GuideStep> _getHistorySteps() {
    return [
      GuideStep(
        title: 'دسترسی به تاریخچه محاسبات',
        content: 'تاریخچه محاسبات از طریق آیکون تاریخچه در صفحه اصلی قابل دسترسی است.\n\n'
            'در این صفحه می‌توانید:\n'
            '• تمام محاسبات قبلی را مشاهده کنید\n'
            '• جزئیات هر محاسبه را ببینید\n'
            '• محاسبات را جستجو کنید\n'
            '• محاسبات غیرضروری را حذف کنید\n'
            '• محاسبات را مرتب‌سازی کنید',
        tips: [
          'محاسبات بر اساس تاریخ مرتب‌سازی می‌شوند',
          'از قابلیت جستجو برای یافتن سریع استفاده کنید',
          'می‌توانید چندین محاسبه را همزمان حذف کنید',
        ],
      ),
      GuideStep(
        title: 'مشاهده جزئیات محاسبات قبلی',
        content: 'با کلیک روی هر محاسبه در تاریخچه:\n\n'
            '• جزئیات کامل محاسبه نمایش داده می‌شود\n'
            '• می‌توانید نتایج را دوباره اشتراک‌گذاری کنید\n'
            '• امکان کپی کردن اطلاعات وجود دارد\n'
            '• می‌توانید بر اساس آن محاسبه جدید انجام دهید\n\n'
            'این قابلیت برای مقایسه محاسبات مختلف مفید است.',
        tips: [
          'جزئیات شامل روش، نقاط/اضلاع، و یادداشت‌ها است',
          'می‌توانید از محاسبات قبلی الگوبرداری کنید',
          'برای پروژه‌های بزرگ، تاریخچه بسیار مفید است',
        ],
      ),
      GuideStep(
        title: 'مدیریت و پاک‌سازی تاریخچه',
        content: 'برای مدیریت بهتر تاریخچه:\n\n'
            '۱. محاسبات قدیمی و غیرضروری را حذف کنید\n'
            '۲. از نام‌گذاری مناسب برای محاسبات جدید استفاده کنید\n'
            '۳. یادداشت‌های توضیحی اضافه کنید\n'
            '۴. در صورت نیاز، تاریخچه را پشتیبان‌گیری کنید\n\n'
            'تاریخچه در حافظه دستگاه ذخیره می‌شود و با حذف برنامه پاک می‌شود.',
        tips: [
          'تاریخچه فضای کمی اشغال می‌کند',
          'حذف محاسبات قابل بازگشت نیست',
          'برای پروژه‌های مهم، نتایج را جداگانه ذخیره کنید',
        ],
      ),
    ];
  }

  static List<GuideStep> _getTipsSteps() {
    return [
      GuideStep(
        title: 'نکات اندازه‌گیری دقیق',
        content: 'برای دستیابی به بهترین نتایج:\n\n'
            '• از ابزارهای اندازه‌گیری دقیق استفاده کنید\n'
            '• اندازه‌گیری‌ها را دو بار انجام دهید\n'
            '• در شرایط آب و هوایی مناسب اندازه‌گیری کنید\n'
            '• از GPS با دقت بالا برای مختصات استفاده کنید\n'
            '• نقاط مرجع ثابت انتخاب کنید\n\n'
            'دقت در اندازه‌گیری، کلید دقت در نتایج است.',
        tips: [
          'متر فلزی دقیق‌تر از متر پارچه‌ای است',
          'GPS در فضای باز دقت بیشتری دارد',
          'باد شدید می‌تواند اندازه‌گیری را تحت تأثیر قرار دهد',
        ],
      ),
      GuideStep(
        title: 'انتخاب روش مناسب',
        content: 'راهنمای انتخاب روش محاسبه:\n\n'
            'روش مختصات را انتخاب کنید اگر:\n'
            '• دسترسی به GPS یا ابزار مختصات‌یابی دارید\n'
            '• زمین شکل پیچیده‌ای دارد\n'
            '• دقت بالا اولویت است\n\n'
            'روش سنتی را انتخاب کنید اگر:\n'
            '• زمین به راحتی به مثلث تقسیم می‌شود\n'
            '• فقط متر یا نوار اندازه‌گیری دارید\n'
            '• زمین شکل نسبتاً ساده دارد',
        tips: [
          'هر دو روش نتایج دقیق ارائه می‌دهند',
          'می‌توانید هر دو روش را امتحان کنید',
          'برای زمین‌های بزرگ، روش مختصات بهتر است',
        ],
      ),
      GuideStep(
        title: 'کاربردهای حرفه‌ای',
        content: 'جریب‌یار برای موارد زیر مناسب است:\n\n'
            '• ارزیابی املاک و زمین\n'
            '• برنامه‌ریزی کشاورزی\n'
            '• محاسبه مساحت برای بیمه\n'
            '• پروژه‌های ساختمانی\n'
            '• تقسیم‌بندی زمین\n'
            '• گزارش‌های رسمی\n\n'
            'نتایج برنامه قابلیت استفاده در اسناد رسمی را دارد.',
        tips: [
          'همیشه یادداشت‌های توضیحی اضافه کنید',
          'نتایج مهم را پشتیبان‌گیری کنید',
          'برای پروژه‌های بزرگ، از چندین روش استفاده کنید',
          'با متخصصان نقشه‌برداری مشورت کنید',
        ],
      ),
    ];
  }
}

/// Custom painter for drawing coordinate system diagram
class CoordinateSystemPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final textPainter = TextPainter(
      textDirection: TextDirection.rtl,
    );

    // Draw axes
    // X axis
    canvas.drawLine(
      Offset(20, size.height - 20),
      Offset(size.width - 20, size.height - 20),
      paint,
    );

    // Y axis
    canvas.drawLine(
      Offset(20, size.height - 20),
      Offset(20, 20),
      paint,
    );

    // Draw arrow heads
    final arrowPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    // X axis arrow
    final xArrowPath = Path();
    xArrowPath.moveTo(size.width - 20, size.height - 20);
    xArrowPath.lineTo(size.width - 30, size.height - 25);
    xArrowPath.lineTo(size.width - 30, size.height - 15);
    xArrowPath.close();
    canvas.drawPath(xArrowPath, arrowPaint);

    // Y axis arrow
    final yArrowPath = Path();
    yArrowPath.moveTo(20, 20);
    yArrowPath.lineTo(15, 30);
    yArrowPath.lineTo(25, 30);
    yArrowPath.close();
    canvas.drawPath(yArrowPath, arrowPaint);

    // Draw labels
    textPainter.text = const TextSpan(
      text: 'X',
      style: TextStyle(color: Colors.blue, fontSize: 16, fontWeight: FontWeight.bold),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(size.width - 15, size.height - 35));

    textPainter.text = const TextSpan(
      text: 'Y',
      style: TextStyle(color: Colors.blue, fontSize: 16, fontWeight: FontWeight.bold),
    );
    textPainter.layout();
    textPainter.paint(canvas, const Offset(5, 15));

    // Draw origin
    textPainter.text = const TextSpan(
      text: '(0,0)',
      style: TextStyle(color: Colors.blue, fontSize: 12),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(25, size.height - 15));

    // Draw sample points
    final pointPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(60, size.height - 60), 4, pointPaint);
    canvas.drawCircle(Offset(120, size.height - 40), 4, pointPaint);
    canvas.drawCircle(Offset(100, size.height - 100), 4, pointPaint);

    // Connect points to show polygon
    final polygonPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final polygonPath = Path();
    polygonPath.moveTo(60, size.height - 60);
    polygonPath.lineTo(120, size.height - 40);
    polygonPath.lineTo(100, size.height - 100);
    polygonPath.close();
    canvas.drawPath(polygonPath, polygonPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
