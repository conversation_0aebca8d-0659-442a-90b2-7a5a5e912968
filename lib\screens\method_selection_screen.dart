import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/constants.dart';
import '../widgets/responsive_layout.dart';
import 'coordinate_input_screen.dart';
import 'traditional_input_screen.dart';

/// Screen for selecting calculation method
class MethodSelectionScreen extends ConsumerWidget {
  const MethodSelectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('انتخاب روش محاسبه'), centerTitle: true),
      body: SafeArea(
        child: ResponsivePadding(
          mobilePadding: const EdgeInsets.all(16),
          tabletPadding: const EdgeInsets.all(24),
          desktopPadding: const EdgeInsets.all(32),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  _buildHeader(context),

                  ResponsiveSpacing(mobile: 24, tablet: 32, desktop: 40),

                  // Method cards
                  Expanded(child: _buildMethodCards(context, constraints)),

                  ResponsiveSpacing(mobile: 16, tablet: 20, desktop: 24),

                  // Help button
                  _buildHelpButton(context),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.calculate,
              size: 48,
              color: const Color(AppConstants.primaryColor),
            ),
            const SizedBox(height: 12),
            Text(
              'انتخاب روش محاسبه',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'لطفاً روش مناسب برای محاسبه مساحت زمین خود انتخاب کنید',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMethodCards(BuildContext context, BoxConstraints constraints) {
    if (constraints.maxWidth > 900) {
      // Desktop layout - side by side
      return Row(
        children: [
          Expanded(
            child: _buildMethodCard(
              context,
              title: 'روش مختصات',
              subtitle: 'الگوریتم Shoelace',
              description: 'محاسبه دقیق با استفاده از مختصات نقاط زمین',
              icon: Icons.timeline,
              color: const Color(AppConstants.primaryColor),
              advantages: [
                'دقت بسیار بالا',
                'مناسب برای اشکال پیچیده',
                'سرعت محاسبه بالا',
                'عدم نیاز به اندازه‌گیری زوایا',
              ],
              onTap: () => _navigateToCoordinateInput(context),
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: _buildMethodCard(
              context,
              title: 'روش سنتی',
              subtitle: 'اضلاع و زوایا',
              description: 'محاسبه با استفاده از طول اضلاع و زوایای زمین',
              icon: Icons.straighten,
              color: const Color(AppConstants.secondaryColor),
              advantages: [
                'روش آشنا و سنتی',
                'مناسب برای اشکال منظم',
                'امکان استفاده از ابزار ساده',
                'مناسب برای مثلث و چهارضلعی',
              ],
              onTap: () => _navigateToTraditionalInput(context),
            ),
          ),
        ],
      );
    } else {
      // Mobile/Tablet layout - stacked
      return Column(
        children: [
          Expanded(
            child: _buildMethodCard(
              context,
              title: 'روش مختصات',
              subtitle: 'الگوریتم Shoelace',
              description: 'محاسبه دقیق با استفاده از مختصات نقاط زمین',
              icon: Icons.timeline,
              color: const Color(AppConstants.primaryColor),
              advantages: [
                'دقت بسیار بالا',
                'مناسب برای اشکال پیچیده',
                'سرعت محاسبه بالا',
                'عدم نیاز به اندازه‌گیری زوایا',
              ],
              onTap: () => _navigateToCoordinateInput(context),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildMethodCard(
              context,
              title: 'روش سنتی',
              subtitle: 'اضلاع و زوایا',
              description: 'محاسبه با استفاده از طول اضلاع و زوایای زمین',
              icon: Icons.straighten,
              color: const Color(AppConstants.secondaryColor),
              advantages: [
                'روش آشنا و سنتی',
                'مناسب برای اشکال منظم',
                'امکان استفاده از ابزار ساده',
                'مناسب برای مثلث و چهارضلعی',
              ],
              onTap: () => _navigateToTraditionalInput(context),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildMethodCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> advantages,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 6,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(icon, color: Colors.white, size: 24),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: color,
                            ),
                          ),
                          Text(
                            subtitle,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios, color: color, size: 20),
                  ],
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Advantages (limited for mobile)
                Text(
                  'مزایا:',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 6),
                ...advantages
                    .take(2)
                    .map(
                      (advantage) => Padding(
                        padding: const EdgeInsets.only(bottom: 3),
                        child: Row(
                          children: [
                            Icon(Icons.check_circle, color: color, size: 14),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                advantage,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(fontSize: 12),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHelpButton(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: () => _showHelpDialog(context),
      icon: const Icon(Icons.help_outline),
      label: const Text('راهنما و توضیحات'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
        side: const BorderSide(color: Color(AppConstants.primaryColor)),
        foregroundColor: const Color(AppConstants.primaryColor),
      ),
    );
  }

  void _navigateToCoordinateInput(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CoordinateInputScreen()),
    );
  }

  void _navigateToTraditionalInput(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TraditionalInputScreen()),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('راهنمای انتخاب روش'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'روش مختصات (Shoelace):',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• مناسب زمانی که مختصات نقاط زمین را دارید\n'
                    '• دقت بسیار بالا برای هر شکل نامنظم\n'
                    '• نیاز به تعیین نقطه مبدأ و اندازه‌گیری مختصات\n'
                    '• بهترین انتخاب برای زمین‌های پیچیده',
                  ),
                  SizedBox(height: 16),
                  Text(
                    'روش سنتی (اضلاع و زوایا):',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '• مناسب برای اشکال ساده مثل مثلث و چهارضلعی\n'
                    '• نیاز به اندازه‌گیری طول اضلاع و زوایا\n'
                    '• روش آشنا و قابل فهم\n'
                    '• محدود به اشکال هندسی منظم',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('متوجه شدم'),
              ),
            ],
          ),
    );
  }
}
