import 'dart:math' as math;
import '../models/coordinate_point.dart';
import '../models/calculation_result.dart';
import '../utils/constants.dart';

/// Service for calculating polygon area using the Shoelace algorithm
class ShoelaceCalculator {
  /// Calculate area using the Shoelace formula (Surveyor's formula)
  /// Formula: Area = 1/2 * |Σ(xi * yi+1 - xi+1 * yi)|
  static double calculateArea(List<CoordinatePoint> vertices) {
    if (vertices.length < 3) {
      throw ArgumentError('At least 3 vertices are required for area calculation');
    }

    double area = 0.0;
    final int n = vertices.length;

    // Apply the Shoelace formula
    for (int i = 0; i < n; i++) {
      final int j = (i + 1) % n;
      area += vertices[i].x * vertices[j].y;
      area -= vertices[j].x * vertices[i].y;
    }

    return area.abs() / 2.0;
  }

  /// Calculate perimeter of the polygon
  static double calculatePerimeter(List<CoordinatePoint> vertices) {
    if (vertices.length < 2) return 0.0;

    double perimeter = 0.0;
    final int n = vertices.length;

    for (int i = 0; i < n; i++) {
      final int j = (i + 1) % n;
      perimeter += vertices[i].distanceTo(vertices[j]);
    }

    return perimeter;
  }

  /// Validate polygon vertices
  static bool validateVertices(List<CoordinatePoint> vertices) {
    // Check minimum number of vertices
    if (vertices.length < AppConstants.minVertices) return false;
    
    // Check maximum number of vertices
    if (vertices.length > AppConstants.maxVertices) return false;

    // Check for valid coordinate values
    for (final vertex in vertices) {
      if (vertex.x < AppConstants.minCoordinateValue ||
          vertex.x > AppConstants.maxCoordinateValue ||
          vertex.y < AppConstants.minCoordinateValue ||
          vertex.y > AppConstants.maxCoordinateValue) {
        return false;
      }
    }

    // Check for duplicate consecutive vertices
    for (int i = 0; i < vertices.length; i++) {
      final int j = (i + 1) % vertices.length;
      if (vertices[i] == vertices[j]) {
        return false;
      }
    }

    return true;
  }

  /// Check if polygon is self-intersecting (basic check)
  static bool isSelfIntersecting(List<CoordinatePoint> vertices) {
    if (vertices.length < 4) return false;

    // Check each edge against all non-adjacent edges
    for (int i = 0; i < vertices.length; i++) {
      final int i1 = (i + 1) % vertices.length;
      final p1 = vertices[i];
      final p2 = vertices[i1];

      for (int j = i + 2; j < vertices.length; j++) {
        if (j == vertices.length - 1 && i == 0) continue; // Skip last-first edge
        
        final int j1 = (j + 1) % vertices.length;
        final p3 = vertices[j];
        final p4 = vertices[j1];

        if (_doLinesIntersect(p1, p2, p3, p4)) {
          return true;
        }
      }
    }

    return false;
  }

  /// Check if two line segments intersect
  static bool _doLinesIntersect(
    CoordinatePoint p1,
    CoordinatePoint p2,
    CoordinatePoint p3,
    CoordinatePoint p4,
  ) {
    final double d1 = _crossProduct(p3, p4, p1);
    final double d2 = _crossProduct(p3, p4, p2);
    final double d3 = _crossProduct(p1, p2, p3);
    final double d4 = _crossProduct(p1, p2, p4);

    if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
        ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
      return true;
    }

    // Check for collinear points
    if (d1 == 0 && _isPointOnSegment(p3, p4, p1)) return true;
    if (d2 == 0 && _isPointOnSegment(p3, p4, p2)) return true;
    if (d3 == 0 && _isPointOnSegment(p1, p2, p3)) return true;
    if (d4 == 0 && _isPointOnSegment(p1, p2, p4)) return true;

    return false;
  }

  /// Calculate cross product for line intersection
  static double _crossProduct(CoordinatePoint a, CoordinatePoint b, CoordinatePoint c) {
    return (c.y - a.y) * (b.x - a.x) - (c.x - a.x) * (b.y - a.y);
  }

  /// Check if point c lies on line segment ab
  static bool _isPointOnSegment(CoordinatePoint a, CoordinatePoint b, CoordinatePoint c) {
    return c.x <= math.max(a.x, b.x) &&
           c.x >= math.min(a.x, b.x) &&
           c.y <= math.max(a.y, b.y) &&
           c.y >= math.min(a.y, b.y);
  }

  /// Calculate centroid of the polygon
  static CoordinatePoint calculateCentroid(List<CoordinatePoint> vertices) {
    if (vertices.isEmpty) return const CoordinatePoint(x: 0, y: 0);

    double area = calculateArea(vertices);
    if (area == 0) {
      // Fallback to arithmetic mean for degenerate cases
      double sumX = 0, sumY = 0;
      for (final vertex in vertices) {
        sumX += vertex.x;
        sumY += vertex.y;
      }
      return CoordinatePoint(x: sumX / vertices.length, y: sumY / vertices.length);
    }

    double cx = 0, cy = 0;
    final int n = vertices.length;

    for (int i = 0; i < n; i++) {
      final int j = (i + 1) % n;
      final double factor = vertices[i].x * vertices[j].y - vertices[j].x * vertices[i].y;
      cx += (vertices[i].x + vertices[j].x) * factor;
      cy += (vertices[i].y + vertices[j].y) * factor;
    }

    final double sixArea = 6.0 * area;
    return CoordinatePoint(x: cx / sixArea, y: cy / sixArea);
  }

  /// Create a complete calculation result
  static CalculationResult calculateComplete({
    required CoordinatePolygon polygon,
    required String unit,
    String? name,
    String? notes,
  }) {
    // Validate input
    if (!validateVertices(polygon.vertices)) {
      throw ArgumentError('Invalid polygon vertices');
    }

    // Check for self-intersection
    if (isSelfIntersecting(polygon.vertices)) {
      throw ArgumentError('Self-intersecting polygon detected');
    }

    // Calculate area and perimeter
    final double area = calculateArea(polygon.vertices);
    final double perimeter = calculatePerimeter(polygon.vertices);

    // Convert to all supported units
    final Map<String, double> areaInAllUnits = {};
    for (final unitKey in AppConstants.areaUnits.keys) {
      final conversionFactor = AppConstants.areaUnits[unitKey]! / AppConstants.areaUnits[unit]!;
      areaInAllUnits[unitKey] = area * conversionFactor;
    }

    return CalculationResult(
      area: area,
      perimeter: perimeter,
      unit: unit,
      method: CalculationMethod.coordinate,
      name: name ?? 'محاسبه مختصاتی ${DateTime.now().millisecondsSinceEpoch}',
      notes: notes,
      inputData: {
        'polygon': polygon.toJson(),
        'vertices_count': polygon.vertices.length,
        'centroid': calculateCentroid(polygon.vertices).toJson(),
        'bounding_box': {
          'min_x': polygon.boundingBox.minX,
          'max_x': polygon.boundingBox.maxX,
          'min_y': polygon.boundingBox.minY,
          'max_y': polygon.boundingBox.maxY,
        },
      },
      areaInAllUnits: areaInAllUnits,
    );
  }

  /// Get polygon statistics
  static Map<String, dynamic> getPolygonStatistics(CoordinatePolygon polygon) {
    if (polygon.vertices.length < 3) {
      return {'error': 'Insufficient vertices'};
    }

    final area = calculateArea(polygon.vertices);
    final perimeter = calculatePerimeter(polygon.vertices);
    final centroid = calculateCentroid(polygon.vertices);
    final boundingBox = polygon.boundingBox;
    final isSelfIntersect = isSelfIntersecting(polygon.vertices);

    return {
      'area': area,
      'perimeter': perimeter,
      'vertices_count': polygon.vertices.length,
      'centroid': centroid.toJson(),
      'bounding_box': {
        'min_x': boundingBox.minX,
        'max_x': boundingBox.maxX,
        'min_y': boundingBox.minY,
        'max_y': boundingBox.maxY,
        'width': boundingBox.maxX - boundingBox.minX,
        'height': boundingBox.maxY - boundingBox.minY,
      },
      'is_self_intersecting': isSelfIntersect,
      'is_valid': !isSelfIntersect && polygon.vertices.length >= 3,
    };
  }
}
