import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/constants.dart';

/// Widget for previewing geometric shapes
class ShapePreviewWidget extends StatelessWidget {
  final String shapeType;
  final Map<String, double> parameters;
  final Color? strokeColor;
  final Color? fillColor;
  final double strokeWidth;

  const ShapePreviewWidget({
    super.key,
    required this.shapeType,
    required this.parameters,
    this.strokeColor,
    this.fillColor,
    this.strokeWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      child: CustomPaint(
        painter: <PERSON>hapePainter(
          shapeType: shapeType,
          parameters: parameters,
          strokeColor: strokeColor ?? const Color(AppConstants.primaryColor),
          fillColor: fillColor ?? const Color(AppConstants.primaryColor).withValues(alpha: 0.1),
          strokeWidth: strokeWidth,
        ),
        child: Container(),
      ),
    );
  }
}

/// Custom painter for drawing geometric shapes
class Sha<PERSON><PERSON>ain<PERSON> extends CustomPainter {
  final String shapeType;
  final Map<String, double> parameters;
  final Color strokeColor;
  final Color fillColor;
  final double strokeWidth;

  ShapePainter({
    required this.shapeType,
    required this.parameters,
    required this.strokeColor,
    required this.fillColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    switch (shapeType) {
      case 'triangle':
        _drawTriangle(canvas, size);
        break;
      case 'rectangle':
        _drawRectangle(canvas, size);
        break;
      case 'regular_polygon':
        _drawRegularPolygon(canvas, size);
        break;
      default:
        _drawPlaceholder(canvas, size);
    }
  }

  void _drawTriangle(Canvas canvas, Size size) {
    final sideA = parameters['side_a'] ?? 0;
    final sideB = parameters['side_b'] ?? 0;
    final sideC = parameters['side_c'] ?? 0;

    if (sideA == 0 || sideB == 0 || sideC == 0) {
      _drawPlaceholder(canvas, size);
      return;
    }

    // Calculate triangle vertices using law of cosines
    final center = Offset(size.width / 2, size.height / 2);
    final scale = _calculateTriangleScale(size, sideA, sideB, sideC);

    // Place first vertex at bottom left
    final vertex1 = Offset(
      center.dx - (sideC * scale) / 2,
      center.dy + (sideA * scale) / 4,
    );

    // Place second vertex at bottom right
    final vertex2 = Offset(
      center.dx + (sideC * scale) / 2,
      center.dy + (sideA * scale) / 4,
    );

    // Calculate third vertex using law of cosines
    final angleC = math.acos((sideA * sideA + sideB * sideB - sideC * sideC) / (2 * sideA * sideB));
    final height = sideB * math.sin(angleC) * scale;
    final baseOffset = sideB * math.cos(angleC) * scale;

    final vertex3 = Offset(
      vertex1.dx + baseOffset,
      vertex1.dy - height,
    );

    final path = Path();
    path.moveTo(vertex1.dx, vertex1.dy);
    path.lineTo(vertex2.dx, vertex2.dy);
    path.lineTo(vertex3.dx, vertex3.dy);
    path.close();

    // Fill
    final fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, fillPaint);

    // Stroke
    final strokePaint = Paint()
      ..color = strokeColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    canvas.drawPath(path, strokePaint);

    // Draw vertices
    _drawVertex(canvas, vertex1, '1');
    _drawVertex(canvas, vertex2, '2');
    _drawVertex(canvas, vertex3, '3');

    // Draw side labels
    _drawSideLabel(canvas, vertex1, vertex2, 'c = ${sideC.toStringAsFixed(1)}');
    _drawSideLabel(canvas, vertex2, vertex3, 'a = ${sideA.toStringAsFixed(1)}');
    _drawSideLabel(canvas, vertex3, vertex1, 'b = ${sideB.toStringAsFixed(1)}');
  }

  void _drawRectangle(Canvas canvas, Size size) {
    final length = parameters['length'] ?? 0;
    final width = parameters['width'] ?? 0;

    if (length == 0 || width == 0) {
      _drawPlaceholder(canvas, size);
      return;
    }

    final center = Offset(size.width / 2, size.height / 2);
    final scale = _calculateRectangleScale(size, length, width);

    final scaledLength = length * scale;
    final scaledWidth = width * scale;

    final rect = Rect.fromCenter(
      center: center,
      width: scaledLength,
      height: scaledWidth,
    );

    // Fill
    final fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(rect, fillPaint);

    // Stroke
    final strokePaint = Paint()
      ..color = strokeColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    canvas.drawRect(rect, strokePaint);

    // Draw dimension labels
    _drawDimensionLabel(
      canvas,
      Offset(rect.left, rect.center.dy),
      Offset(rect.right, rect.center.dy),
      '${length.toStringAsFixed(1)} m',
      true,
    );
    _drawDimensionLabel(
      canvas,
      Offset(rect.center.dx, rect.top),
      Offset(rect.center.dx, rect.bottom),
      '${width.toStringAsFixed(1)} m',
      false,
    );
  }

  void _drawRegularPolygon(Canvas canvas, Size size) {
    final sides = parameters['sides']?.toInt() ?? 0;
    final sideLength = parameters['side_length'] ?? 0;

    if (sides < 3 || sideLength == 0) {
      _drawPlaceholder(canvas, size);
      return;
    }

    final center = Offset(size.width / 2, size.height / 2);
    final radius = _calculatePolygonRadius(size, sides, sideLength);

    final path = Path();
    for (int i = 0; i < sides; i++) {
      final angle = (2 * math.pi * i) / sides - math.pi / 2;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    // Fill
    final fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, fillPaint);

    // Stroke
    final strokePaint = Paint()
      ..color = strokeColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    canvas.drawPath(path, strokePaint);
  }

  void _drawPlaceholder(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw placeholder icon
    final textPainter = TextPainter(
      text: const TextSpan(
        text: '📐',
        style: TextStyle(fontSize: 48),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  void _drawVertex(Canvas canvas, Offset position, String label) {
    final paint = Paint()
      ..color = strokeColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(position, 4, paint);

    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  void _drawSideLabel(Canvas canvas, Offset start, Offset end, String label) {
    final midpoint = Offset(
      (start.dx + end.dx) / 2,
      (start.dy + end.dy) / 2,
    );

    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: strokeColor,
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    // Draw background
    final bgPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: midpoint,
          width: textPainter.width + 8,
          height: textPainter.height + 4,
        ),
        const Radius.circular(4),
      ),
      bgPaint,
    );

    textPainter.paint(
      canvas,
      Offset(
        midpoint.dx - textPainter.width / 2,
        midpoint.dy - textPainter.height / 2,
      ),
    );
  }

  void _drawDimensionLabel(
    Canvas canvas,
    Offset start,
    Offset end,
    String label,
    bool horizontal,
  ) {
    final midpoint = Offset(
      (start.dx + end.dx) / 2,
      (start.dy + end.dy) / 2,
    );

    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: strokeColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    final offset = horizontal
        ? Offset(midpoint.dx - textPainter.width / 2, midpoint.dy - 20)
        : Offset(midpoint.dx + 15, midpoint.dy - textPainter.height / 2);

    // Draw background
    final bgPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: offset.translate(textPainter.width / 2, textPainter.height / 2),
          width: textPainter.width + 8,
          height: textPainter.height + 4,
        ),
        const Radius.circular(4),
      ),
      bgPaint,
    );

    textPainter.paint(canvas, offset);
  }

  double _calculateTriangleScale(Size size, double sideA, double sideB, double sideC) {
    final maxSide = math.max(math.max(sideA, sideB), sideC);
    final availableSize = math.min(size.width, size.height) * 0.7;
    return availableSize / maxSide;
  }

  double _calculateRectangleScale(Size size, double length, double width) {
    final scaleX = (size.width * 0.8) / length;
    final scaleY = (size.height * 0.8) / width;
    return math.min(scaleX, scaleY);
  }

  double _calculatePolygonRadius(Size size, int sides, double sideLength) {
    final circumradius = sideLength / (2 * math.sin(math.pi / sides));
    final availableRadius = math.min(size.width, size.height) * 0.4;
    return math.min(circumradius * 20, availableRadius);
  }

  @override
  bool shouldRepaint(ShapePainter oldDelegate) {
    return oldDelegate.shapeType != shapeType ||
           oldDelegate.parameters != parameters ||
           oldDelegate.strokeColor != strokeColor ||
           oldDelegate.fillColor != fillColor ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}
