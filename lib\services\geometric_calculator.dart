import 'dart:math' as math;
import '../models/calculation_result.dart';
import '../models/triangle.dart';
import '../utils/constants.dart';

/// Service for calculating area using traditional geometric methods
class GeometricCalculator {
  /// Calculate triangle area using <PERSON><PERSON>'s formula
  /// Formula: Area = √(s(s-a)(s-b)(s-c)) where s = (a+b+c)/2
  static double calculateTriangleAreaHeron(double sideA, double sideB, double sideC) {
    // Validate triangle inequality
    if (!_isValidTriangle(sideA, sideB, sideC)) {
      throw ArgumentError('Invalid triangle: sides do not satisfy triangle inequality');
    }

    // Calculate semi-perimeter
    final double s = (sideA + sideB + sideC) / 2;

    // Apply <PERSON>n's formula
    final double area = math.sqrt(s * (s - sideA) * (s - sideB) * (s - sideC));

    return area;
  }

  /// Calculate triangle area using base and height
  static double calculateTriangleAreaBaseHeight(double base, double height) {
    if (base <= 0 || height <= 0) {
      throw ArgumentError('Base and height must be positive');
    }
    return (base * height) / 2;
  }

  /// Calculate triangle area using two sides and included angle (SAS)
  /// Formula: Area = (1/2) * a * b * sin(C)
  static double calculateTriangleAreaSAS(double sideA, double sideB, double angleC) {
    if (sideA <= 0 || sideB <= 0) {
      throw ArgumentError('Sides must be positive');
    }
    if (angleC <= 0 || angleC >= 180) {
      throw ArgumentError('Angle must be between 0 and 180 degrees');
    }

    // Convert angle to radians
    final double angleRadians = angleC * math.pi / 180;
    
    return 0.5 * sideA * sideB * math.sin(angleRadians);
  }

  /// Calculate rectangle area
  static double calculateRectangleArea(double length, double width) {
    if (length <= 0 || width <= 0) {
      throw ArgumentError('Length and width must be positive');
    }
    return length * width;
  }

  /// Calculate parallelogram area
  static double calculateParallelogramArea(double base, double height) {
    if (base <= 0 || height <= 0) {
      throw ArgumentError('Base and height must be positive');
    }
    return base * height;
  }

  /// Calculate trapezoid area
  static double calculateTrapezoidArea(double base1, double base2, double height) {
    if (base1 <= 0 || base2 <= 0 || height <= 0) {
      throw ArgumentError('All measurements must be positive');
    }
    return ((base1 + base2) * height) / 2;
  }

  /// Calculate regular polygon area
  static double calculateRegularPolygonArea(int sides, double sideLength) {
    if (sides < 3) {
      throw ArgumentError('Polygon must have at least 3 sides');
    }
    if (sideLength <= 0) {
      throw ArgumentError('Side length must be positive');
    }

    // Formula: Area = (n * s²) / (4 * tan(π/n))
    final double n = sides.toDouble();
    final double area = (n * sideLength * sideLength) / (4 * math.tan(math.pi / n));
    
    return area;
  }

  /// Validate triangle inequality theorem
  static bool _isValidTriangle(double a, double b, double c) {
    if (a <= 0 || b <= 0 || c <= 0) return false;
    return (a + b > c) && (a + c > b) && (b + c > a);
  }

  /// Calculate triangle perimeter
  static double calculateTrianglePerimeter(double sideA, double sideB, double sideC) {
    if (!_isValidTriangle(sideA, sideB, sideC)) {
      throw ArgumentError('Invalid triangle sides');
    }
    return sideA + sideB + sideC;
  }

  /// Calculate rectangle perimeter
  static double calculateRectanglePerimeter(double length, double width) {
    if (length <= 0 || width <= 0) {
      throw ArgumentError('Length and width must be positive');
    }
    return 2 * (length + width);
  }

  /// Calculate regular polygon perimeter
  static double calculateRegularPolygonPerimeter(int sides, double sideLength) {
    if (sides < 3 || sideLength <= 0) {
      throw ArgumentError('Invalid polygon parameters');
    }
    return sides * sideLength;
  }

  /// Create complete calculation result for triangle using Heron's formula
  static CalculationResult calculateTriangleComplete({
    required double sideA,
    required double sideB,
    required double sideC,
    required String unit,
    String? name,
    String? notes,
  }) {
    // Validate input
    if (!_isValidTriangle(sideA, sideB, sideC)) {
      throw ArgumentError('Invalid triangle: sides do not satisfy triangle inequality');
    }

    // Calculate area and perimeter
    final double area = calculateTriangleAreaHeron(sideA, sideB, sideC);
    final double perimeter = calculateTrianglePerimeter(sideA, sideB, sideC);

    // Convert to all supported units
    final Map<String, double> areaInAllUnits = {};
    for (final unitKey in AppConstants.areaUnits.keys) {
      final conversionFactor = AppConstants.areaUnits[unitKey]! / AppConstants.areaUnits[unit]!;
      areaInAllUnits[unitKey] = area * conversionFactor;
    }

    // Calculate additional triangle properties
    final double semiPerimeter = perimeter / 2;
    final double inradius = area / semiPerimeter;
    final double circumradius = (sideA * sideB * sideC) / (4 * area);

    return CalculationResult(
      area: area,
      perimeter: perimeter,
      unit: unit,
      method: CalculationMethod.traditional,
      name: name ?? 'محاسبه مثلث ${DateTime.now().millisecondsSinceEpoch}',
      notes: notes,
      inputData: {
        'shape_type': 'triangle',
        'method': 'heron',
        'side_a': sideA,
        'side_b': sideB,
        'side_c': sideC,
        'semi_perimeter': semiPerimeter,
        'inradius': inradius,
        'circumradius': circumradius,
        'triangle_type': _getTriangleType(sideA, sideB, sideC),
      },
      areaInAllUnits: areaInAllUnits,
    );
  }

  /// Create complete calculation result for rectangle
  static CalculationResult calculateRectangleComplete({
    required double length,
    required double width,
    required String unit,
    String? name,
    String? notes,
  }) {
    // Calculate area and perimeter
    final double area = calculateRectangleArea(length, width);
    final double perimeter = calculateRectanglePerimeter(length, width);

    // Convert to all supported units
    final Map<String, double> areaInAllUnits = {};
    for (final unitKey in AppConstants.areaUnits.keys) {
      final conversionFactor = AppConstants.areaUnits[unitKey]! / AppConstants.areaUnits[unit]!;
      areaInAllUnits[unitKey] = area * conversionFactor;
    }

    // Calculate diagonal
    final double diagonal = math.sqrt(length * length + width * width);

    return CalculationResult(
      area: area,
      perimeter: perimeter,
      unit: unit,
      method: CalculationMethod.traditional,
      name: name ?? 'محاسبه مستطیل ${DateTime.now().millisecondsSinceEpoch}',
      notes: notes,
      inputData: {
        'shape_type': 'rectangle',
        'length': length,
        'width': width,
        'diagonal': diagonal,
        'aspect_ratio': length / width,
      },
      areaInAllUnits: areaInAllUnits,
    );
  }

  /// Determine triangle type based on sides
  static String _getTriangleType(double a, double b, double c) {
    // Sort sides
    final sides = [a, b, c]..sort();
    final double small = sides[0];
    final double medium = sides[1];
    final double large = sides[2];

    // Check for right triangle (Pythagorean theorem)
    const double tolerance = 1e-10;
    if ((small * small + medium * medium - large * large).abs() < tolerance) {
      return 'right_triangle';
    }

    // Check for equilateral triangle
    if ((a - b).abs() < tolerance && (b - c).abs() < tolerance) {
      return 'equilateral_triangle';
    }

    // Check for isosceles triangle
    if ((a - b).abs() < tolerance || (b - c).abs() < tolerance || (a - c).abs() < tolerance) {
      return 'isosceles_triangle';
    }

    // Check for acute or obtuse triangle
    if (small * small + medium * medium > large * large) {
      return 'acute_triangle';
    } else {
      return 'obtuse_triangle';
    }
  }

  /// Get triangle properties for display
  static Map<String, dynamic> getTriangleProperties({
    required double sideA,
    required double sideB,
    required double sideC,
  }) {
    if (!_isValidTriangle(sideA, sideB, sideC)) {
      return {'error': 'Invalid triangle'};
    }

    final area = calculateTriangleAreaHeron(sideA, sideB, sideC);
    final perimeter = calculateTrianglePerimeter(sideA, sideB, sideC);
    final semiPerimeter = perimeter / 2;
    final inradius = area / semiPerimeter;
    final circumradius = (sideA * sideB * sideC) / (4 * area);
    final triangleType = _getTriangleType(sideA, sideB, sideC);

    // Calculate angles using law of cosines
    final angleA = math.acos((sideB * sideB + sideC * sideC - sideA * sideA) / (2 * sideB * sideC));
    final angleB = math.acos((sideA * sideA + sideC * sideC - sideB * sideB) / (2 * sideA * sideC));
    final angleC = math.acos((sideA * sideA + sideB * sideB - sideC * sideC) / (2 * sideA * sideB));

    return {
      'area': area,
      'perimeter': perimeter,
      'semi_perimeter': semiPerimeter,
      'inradius': inradius,
      'circumradius': circumradius,
      'triangle_type': triangleType,
      'angles': {
        'angle_a': angleA * 180 / math.pi, // Convert to degrees
        'angle_b': angleB * 180 / math.pi,
        'angle_c': angleC * 180 / math.pi,
      },
      'is_valid': true,
    };
  }

  /// Validate geometric shape inputs
  static bool validateTriangleInputs(double sideA, double sideB, double sideC) {
    return sideA >= AppConstants.minSideLength &&
           sideA <= AppConstants.maxSideLength &&
           sideB >= AppConstants.minSideLength &&
           sideB <= AppConstants.maxSideLength &&
           sideC >= AppConstants.minSideLength &&
           sideC <= AppConstants.maxSideLength &&
           _isValidTriangle(sideA, sideB, sideC);
  }

  /// Validate rectangle inputs
  static bool validateRectangleInputs(double length, double width) {
    return length >= AppConstants.minSideLength &&
           length <= AppConstants.maxSideLength &&
           width >= AppConstants.minSideLength &&
           width <= AppConstants.maxSideLength;
  }

  /// Create complete calculation result for multiple triangles
  static CalculationResult calculateMultiTriangleComplete({
    required MultiTriangleShape shape,
    required String unit,
    String? name,
    String? notes,
  }) {
    // Validate that we have at least one valid triangle
    if (shape.triangles.isEmpty || shape.validTriangleCount == 0) {
      throw ArgumentError('At least one valid triangle is required');
    }

    double totalArea = 0.0;
    double totalPerimeter = 0.0;
    final List<Map<String, dynamic>> triangleDetails = [];

    // Calculate area and perimeter for each valid triangle
    for (int i = 0; i < shape.triangles.length; i++) {
      final triangle = shape.triangles[i];

      if (triangle.isValid) {
        final area = calculateTriangleAreaHeron(
          triangle.sideA,
          triangle.sideB,
          triangle.sideC,
        );
        final perimeter = calculateTrianglePerimeter(
          triangle.sideA,
          triangle.sideB,
          triangle.sideC,
        );

        totalArea += area;
        totalPerimeter += perimeter;

        // Store individual triangle details
        triangleDetails.add({
          'index': i + 1,
          'side_a': triangle.sideA,
          'side_b': triangle.sideB,
          'side_c': triangle.sideC,
          'area': area,
          'perimeter': perimeter,
          'triangle_type': _getTriangleType(triangle.sideA, triangle.sideB, triangle.sideC),
        });
      }
    }

    // Convert to all supported units
    final Map<String, double> areaInAllUnits = {};
    for (final unitKey in AppConstants.areaUnits.keys) {
      final conversionFactor = AppConstants.areaUnits[unitKey]! / AppConstants.areaUnits[unit]!;
      areaInAllUnits[unitKey] = totalArea * conversionFactor;
    }

    return CalculationResult(
      area: totalArea,
      perimeter: totalPerimeter,
      unit: unit,
      method: CalculationMethod.traditional,
      name: name ?? 'محاسبه چند مثلث ${DateTime.now().millisecondsSinceEpoch}',
      notes: notes,
      inputData: {
        'shape_type': 'multi_triangle',
        'method': 'heron_multiple',
        'triangle_count': shape.triangles.length,
        'valid_triangle_count': shape.validTriangleCount,
        'triangles': triangleDetails,
        'total_area': totalArea,
        'total_perimeter': totalPerimeter,
      },
      areaInAllUnits: areaInAllUnits,
    );
  }

  /// Validate multi-triangle inputs
  static bool validateMultiTriangleInputs(MultiTriangleShape shape) {
    return shape.triangles.isNotEmpty && shape.validTriangleCount > 0;
  }
}
