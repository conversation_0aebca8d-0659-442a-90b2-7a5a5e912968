import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/calculation_result.dart';
import '../utils/constants.dart';

/// Service for managing calculation history using SharedPreferences
class CalculationHistoryService {
  static const String _historyKey = AppConstants.calculationHistoryKey;

  /// Get the calculation history
  static Future<CalculationHistory> getHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey);
      
      if (historyJson == null) {
        return const CalculationHistory(results: []);
      }
      
      final historyData = jsonDecode(historyJson) as Map<String, dynamic>;
      return CalculationHistory.fromJson(historyData);
    } catch (e) {
      // If there's an error reading history, return empty history
      return const CalculationHistory(results: []);
    }
  }

  /// Save a calculation result to history
  static Future<bool> saveCalculation(CalculationResult result) async {
    try {
      final currentHistory = await getHistory();
      final updatedHistory = currentHistory.addResult(result);
      
      final prefs = await SharedPreferences.getInstance();
      final historyJson = jsonEncode(updatedHistory.toJson());
      
      return await prefs.setString(_historyKey, historyJson);
    } catch (e) {
      return false;
    }
  }

  /// Remove a calculation from history by index
  static Future<bool> removeCalculation(int index) async {
    try {
      final currentHistory = await getHistory();
      final updatedHistory = currentHistory.removeResultAt(index);
      
      final prefs = await SharedPreferences.getInstance();
      final historyJson = jsonEncode(updatedHistory.toJson());
      
      return await prefs.setString(_historyKey, historyJson);
    } catch (e) {
      return false;
    }
  }

  /// Clear all calculation history
  static Future<bool> clearHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_historyKey);
    } catch (e) {
      return false;
    }
  }

  /// Get recent calculations (last n results)
  static Future<List<CalculationResult>> getRecentCalculations(int count) async {
    final history = await getHistory();
    return history.getRecentResults(count);
  }

  /// Get calculations by method
  static Future<List<CalculationResult>> getCalculationsByMethod(
    CalculationMethod method,
  ) async {
    final history = await getHistory();
    return history.getResultsByMethod(method);
  }

  /// Get total number of calculations
  static Future<int> getCalculationCount() async {
    final history = await getHistory();
    return history.length;
  }

  /// Check if history is empty
  static Future<bool> isHistoryEmpty() async {
    final history = await getHistory();
    return history.isEmpty;
  }
}
