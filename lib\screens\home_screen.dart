import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/constants.dart';
import '../widgets/responsive_layout.dart';
import 'method_selection_screen.dart';
import 'history_screen.dart';
import 'about_screen.dart';

/// Home screen of the Jarib-Yar application
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const HistoryScreen(),
                ),
              );
            },
            icon: const Icon(Icons.history),
            tooltip: 'تاریخچه محاسبات',
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AboutScreen(),
                ),
              );
            },
            icon: const Icon(Icons.info_outline),
            tooltip: 'درباره برنامه',
          ),
        ],
      ),
      body: SafeArea(
        child: ResponsivePadding(
          mobilePadding: const EdgeInsets.all(16),
          tabletPadding: const EdgeInsets.all(24),
          desktopPadding: const EdgeInsets.all(32),
          child: LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth > 1200) {
                return _buildDesktopLayout(context);
              } else if (constraints.maxWidth > 600) {
                return _buildTabletLayout(context);
              } else {
                return _buildMobileLayout(context);
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildWelcomeSection(context),
          const ResponsiveSpacing(mobile: 24),
          _buildMainActionButton(context),
          const ResponsiveSpacing(mobile: 20),
          _buildFeatureHighlights(context),
          const ResponsiveSpacing(mobile: 32),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildWelcomeSection(context),
          const ResponsiveSpacing(tablet: 32),
          Row(
            children: [
              Expanded(flex: 2, child: _buildMainActionButton(context)),
              const SizedBox(width: 24),
              Expanded(
                flex: 1,
                child: Container(), // Placeholder for additional content
              ),
            ],
          ),
          const ResponsiveSpacing(tablet: 28),
          _buildFeatureHighlights(context),
          const ResponsiveSpacing(tablet: 40),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Left side - Welcome and action
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildWelcomeSection(context),
                const ResponsiveSpacing(desktop: 40),
                _buildMainActionButton(context),
                const ResponsiveSpacing(desktop: 48),
                _buildFooter(context),
              ],
            ),
          ),
        ),

        const SizedBox(width: 48),

        // Right side - Features
        Expanded(
          flex: 1,
          child: SingleChildScrollView(child: _buildFeatureHighlights(context)),
        ),
      ],
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // App icon/logo placeholder
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(AppConstants.primaryColor),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(Icons.landscape, size: 40, color: Colors.white),
            ),

            const SizedBox(height: 16),

            // App title
            Text(
              AppConstants.appName,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(AppConstants.primaryColor),
              ),
            ),

            const SizedBox(height: 8),

            // App description
            Text(
              AppConstants.appDescription,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainActionButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MethodSelectionScreen(),
          ),
        );
      },
      icon: const Icon(Icons.calculate, size: 24),
      label: const Text(
        'شروع محاسبه مساحت',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildFeatureHighlights(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ویژگی‌های کلیدی:',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),

        const SizedBox(height: 16),

        _buildFeatureItem(
          context,
          icon: Icons.timeline,
          title: 'الگوریتم Shoelace',
          description: 'محاسبه دقیق با استفاده از مختصات نقاط',
        ),

        const SizedBox(height: 12),

        _buildFeatureItem(
          context,
          icon: Icons.straighten,
          title: 'روش سنتی',
          description: 'محاسبه با استفاده از طول اضلاع و زوایا',
        ),

        const SizedBox(height: 12),

        _buildFeatureItem(
          context,
          icon: Icons.swap_horiz,
          title: 'تبدیل واحدها',
          description: 'پشتیبانی از متر مربع، جریب، هکتار و سایر واحدها',
        ),

        const SizedBox(height: 12),

        _buildFeatureItem(
          context,
          icon: Icons.history,
          title: 'تاریخچه محاسبات',
          description: 'ذخیره و مشاهده محاسبات قبلی',
        ),
      ],
    );
  }

  Widget _buildFeatureItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(AppConstants.accentColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                description,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 8),
        Text(
          'نسخه ۱.۰.۰',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
        ),
        const SizedBox(height: 4),
        Text(
          'برای محاسبه دقیق مساحت زمین‌های نامنظم',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
