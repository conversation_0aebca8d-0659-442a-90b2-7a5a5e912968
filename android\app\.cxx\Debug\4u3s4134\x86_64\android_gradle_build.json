{"buildFiles": ["C:\\DEV\\MobDev\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\DEV\\MobDev\\android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0.projects\\Mobile\\Myself Pr\\jiribyar\\android\\app\\.cxx\\Debug\\4u3s4134\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\DEV\\MobDev\\android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0.projects\\Mobile\\Myself Pr\\jiribyar\\android\\app\\.cxx\\Debug\\4u3s4134\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\DEV\\MobDev\\android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\DEV\\MobDev\\android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}