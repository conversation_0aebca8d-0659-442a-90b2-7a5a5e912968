import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing user onboarding and first-time experience
class OnboardingService {
  static const String _keyFirstLaunch = 'first_launch';
  static const String _keyOnboardingCompleted = 'onboarding_completed';
  static const String _keyUserGuideShown = 'user_guide_shown';

  /// Check if this is the first time the app is launched
  static Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyFirstLaunch) ?? true;
  }

  /// Mark that the app has been launched before
  static Future<void> markFirstLaunchComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyFirstLaunch, false);
  }

  /// Check if onboarding has been completed
  static Future<bool> isOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyOnboardingCompleted) ?? false;
  }

  /// Mark onboarding as completed
  static Future<void> markOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyOnboardingCompleted, true);
  }

  /// Check if user guide has been shown
  static Future<bool> hasUserGuideBeenShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyUserGuideShown) ?? false;
  }

  /// Mark user guide as shown
  static Future<void> markUserGuideShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyUserGuideShown, true);
  }

  /// Reset all onboarding flags (for testing purposes)
  static Future<void> resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyFirstLaunch);
    await prefs.remove(_keyOnboardingCompleted);
    await prefs.remove(_keyUserGuideShown);
  }

  /// Show first-time user guide if needed
  static Future<bool> shouldShowUserGuide() async {
    final isFirst = await isFirstLaunch();
    final hasShown = await hasUserGuideBeenShown();
    return isFirst || !hasShown;
  }
}
